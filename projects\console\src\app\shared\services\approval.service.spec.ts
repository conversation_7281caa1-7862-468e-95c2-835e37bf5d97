import { TestBed } from '@angular/core/testing';
import { ApprovalService } from './approval.service';

describe('ApprovalService', () => {
  let service: ApprovalService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(ApprovalService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should approve an item', () => {
    const result = service.approveTool(1);
    expect(result).toBeTrue();
  });

  it('should reject an item', () => {
    const result = service.rejectTool(2);
    expect(result).toBeTrue();
  });
});
