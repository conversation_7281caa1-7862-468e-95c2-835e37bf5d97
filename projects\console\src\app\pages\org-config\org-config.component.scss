.org-config-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.org-config-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;

  h2 {
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
  }
  .dropdown-row {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  .dropdown-group {
    display: flex;
    flex-direction: column;
    min-width: 180px;
    align-items: center;
  }
  label {
    margin-bottom: 0.5rem;
    font-weight: 500;
  }
  select {
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #ccc;
    font-size: 1rem;
  }
}

.apply-filters-btn-row {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
} 