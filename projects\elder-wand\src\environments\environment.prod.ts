// Helper function to safely get environment variables from window.env
const getRequiredEnv = (key: string): string => {
  interface EnvWindow extends Window { env?: Record<string, string>; }
  const envWindow = window as EnvWindow;
  const value = envWindow.env?.[key];
  if (value === undefined || value === null) {
    throw new Error(`Environment variable '${key}' is not defined in window.env.`);
  }
  return String(value);
};

const dynamicBaseUrl: string = getRequiredEnv('baseUrl');

export const environment = {
  production: true,
  // experienceStudioUrl: getRequiredEnv('experienceStudioUrl'),
  // productStudioUrl: getRequiredEnv('productStudioUrl'),
  elderWandApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),
  elderWandRedirectUrl: getRequiredEnv('elderWandUrl'),
  apiBaseUrl: getRequiredEnv('baseUrl'),
  apiUrl: getRequiredEnv('baseUrl'),
  experienceStudioUrl:  getRequiredEnv('experienceStudioUrl'),
  productStudioUrl:  getRequiredEnv('productStudioUrl'),
  consoleRedirectUrl:  getRequiredEnv('consoleRedirectUrl'),
  consoleUrl:  getRequiredEnv('consoleUrl'),
  consoleRedirectUri:  getRequiredEnv('consoleRedirectUri'), // Often same as consoleRedirectUrl

  // API Configuration (constructed dynamically or directly from window.env)
  // accessKey: getRequiredEnv('accessKey'),
  apiVersion: getRequiredEnv('apiVersion'),
  baseUrl: getRequiredEnv('baseUrl'), // The base URL itself
  consoleApi:  getRequiredEnv('consoleApi'),
  consoleApiV2:  getRequiredEnv('consoleApiV2'),
  consoleApiAuthUrl:  getRequiredEnv('consoleApiAuthUrl'),
  consoleEmbeddingApi:  getRequiredEnv('consoleEmbeddingApi'),
  consoleInstructionApi:  getRequiredEnv('consoleInstructionApi'),
  consoleLangfuseUrl: getRequiredEnv('consoleLangfuseUrl'),
  consoleTruelensUrl:  getRequiredEnv('consoleTruelensUrl'),
  consolePipelineApi:  getRequiredEnv('consolePipelineApi'),
  experienceApiUrl:  getRequiredEnv('experienceApiUrl'),
  productApiUrl:  getRequiredEnv('productApiUrl'),

  // Logging and App Specific
  enableLogStreaming: getRequiredEnv('enableLogStreaming'),
  logStreamingApiUrl:  getRequiredEnv('logStreamingApiUrl'),
  appVersion: getRequiredEnv('appVersion'),
  workflowExecutionMode: getRequiredEnv('workflowExecutionMode'),
  useBasicLogin: getRequiredEnv('useBasicLogin'), // Assuming this is a string "true" or "false"
  getApiUrl: (endpoint: string) => {
    const baseUrl = getRequiredEnv('baseUrl');
    return `${baseUrl}${endpoint}`;
  }
};