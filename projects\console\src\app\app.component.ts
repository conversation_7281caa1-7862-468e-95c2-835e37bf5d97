import {
  Compo<PERSON>,
  On<PERSON>nit,
  On<PERSON><PERSON><PERSON>,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import {
  SharedAppHeaderComponent,
  HeaderConfig,
} from '@shared/components/app-header/app-header.component';
import { consoleHeaderConfig } from './config/header.config';
import { CubicalLoadingComponent } from '@ava/play-comp-library';
import { DrawerService } from './shared/services/drawer/drawer.service';
import { ThemeService } from './shared/services/theme/theme.service';
import { LoaderService } from './shared/services/loader/loader.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';

import { environment } from '../environments/environment';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    SharedAppHeaderComponent,
    CubicalLoadingComponent,
    CommonModule,
  ],

  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit, OnDestroy {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  showHeaderAndNav: boolean = true;
  showHeroSection: boolean = true;
  isLaunchpadRoute: boolean = false;
  isLoading: boolean = false;
  private loaderSubscription: Subscription;

  // Header configuration
  headerConfig: HeaderConfig = consoleHeaderConfig;

  constructor(
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    private drawerService: DrawerService,
    private themeService: ThemeService,
    private authService: AuthService,
    private router: Router,
    private loaderService: LoaderService,
    private centralizedRedirectService: CentralizedRedirectService,
  ) {
    this.loaderSubscription = this.loaderService.isLoading$.subscribe(
      (loading) => {
        this.isLoading = loading;
      },
    );
  }

  ngAfterViewInit() {
    this.drawerService.registerViewContainer(this.dialogHost);
  }

  ngOnInit(): void {
    const savedTheme = this.themeService.getCurrentTheme();
    this.themeService.setTheme(savedTheme);

    const authConfig: AuthConfig = {
      apiAuthUrl: environment.consoleApiAuthUrl,
      redirectUrl: environment.consoleRedirectUrl,
      postLoginRedirectUrl: '/',
      appName: 'console',
    };
    this.authService.setAuthConfig(authConfig);

    // Check authentication status and redirect if needed
    if (!this.checkAuthenticationAndRedirect()) {
      return; // Don't continue if not authenticated
    }

    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();

    // org_path is now set during login, no need to check here

    this.router.events.subscribe(() => {
      if (this.router.url === '/login') {
        this.showHeaderAndNav = false;
        this.showHeroSection = false;
        this.isLaunchpadRoute = false;
      } else if (this.router.url === '/marketplace') {
        // Hide header and nav for marketplace (it has its own layout)
        this.showHeaderAndNav = false;
        this.showHeroSection = false;
        this.isLaunchpadRoute = false;
      } else if (this.router.url === '/' || this.router.url === '') {
        // Hide header and nav for root redirect component
        // this.showHeaderAndNav = false;
        this.showHeroSection = false;
        this.isLaunchpadRoute = false;
      } else {
        this.showHeaderAndNav = true;
        // Hide hero section for my-agent-home page, show for all other pages
        this.showHeroSection = this.router.url !== '/my-agent-home';
        // Show background image only for dashboard (launchpad) route
        this.isLaunchpadRoute =
          this.router.url === '/dashboard' || this.router.url === '/';
      }
    });
  }

  ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
    this.loaderSubscription.unsubscribe();
  }

  // Simple authentication check
  private checkAuthenticationAndRedirect(): boolean {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();

    if (!accessToken && !refreshToken) {
      // Store current URL and redirect to marketing login
      this.centralizedRedirectService.storeIntendedDestination(
        window.location.href,
      );
      this.centralizedRedirectService.redirectToMarketingLogin();
      return false;
    }
    return true;
  }

  onNavigation(route: string): void {
    console.log('Console Navigation to:', route);
  }

  onProfileAction(action: string): void {
    console.log('Profile action:', action);
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    this.themeService.setTheme(theme);
  }

  onOrgConfigChange(configData: any): void {
    console.log('Org config changed:', configData);
  }

  onLanguageChange(languageCode: string): void {
    console.log('Language changed to:', languageCode);
  }
}
