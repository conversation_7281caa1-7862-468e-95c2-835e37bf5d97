import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { TokenStorageService } from 'projects/shared/auth/services/token-storage.service';

// New interfaces for the API response
interface Team {
  teamId: number;
  teamName: string;
}

interface Project {
  projectId: number;
  projectName: string;
  teams: Team[];
}

interface Domain {
  domainId: number;
  domainName: string;
  projects: Project[];
}

interface Organization {
  orgId: number;
  organizationName: string;
  domains: Domain[];
}

@Injectable({
  providedIn: 'root',
})
export class OrgConfigService {
  private AuthApiUrl = environment.consoleApiAuthUrl;
  private http = inject(HttpClient);
  private tokenStorageService = inject(TokenStorageService);

  constructor() {}

  public getOrganizationHierarchy() {
    const url = `${this.AuthApiUrl}/organization/hierarchy`;
    return this.http.get<Organization[]>(url);
  }

  // public getLevelList(levelNum: number, parentLvlId: number) {
  //   const url = `${this.adminServiceUrl}/ava/force/level?levelNum=${levelNum}&parentLvlId=${parentLvlId}`;
  //   return this.http.get(url).pipe(
  //     map((response: any) => {
  //       return response;
  //     })
  //   );
  // }

  public getUseCaseIdentifier(usecaseCode: string) {
    const path = this.tokenStorageService.getCookie('org_path');
    let usecaseIdentifier = '';
    if (path) {
      const orgPath = path?.split('::')[0]?.replace(/ /g, '_')?.toUpperCase();
      usecaseIdentifier = `${usecaseCode}@${orgPath}`;
    }
    return usecaseIdentifier;
  }

  public getOraganizationPath() {
    const path = this.tokenStorageService.getCookie('org_path');
    let OraganizationPath = '';
    if (path) {
      const orgPath = path?.split('::')[0]?.replace(/ /g, '_')?.toUpperCase();
      OraganizationPath = orgPath;
    }
    return OraganizationPath;
  }
} 