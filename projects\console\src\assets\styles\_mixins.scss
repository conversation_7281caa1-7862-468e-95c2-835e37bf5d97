// Theme utility mixins

// Helper mixin to apply themed CSS variables to properties
@mixin themeify($property, $variable) {
  #{$property}: var(#{$variable});
}

// Helper for transition of themed properties
@mixin theme-transition($properties...) {
  transition-property: $properties;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}

// Apply a gradient border
@mixin gradient-border($width: 1px, $radius: 12px) {
  position: relative;
  border-radius: $radius;
  background-clip: padding-box;
  
  &::before {
    content: '';
    position: absolute;
    top: 0; right: 0; bottom: 0; left: 0;
    z-index: -1;
    margin: -$width;
    border-radius: inherit;
    background: var(--gradient-primary);
  }
}

// Apply standard card styling
@mixin card-style {
  @include themeify('background', '--card-bg');
  @include themeify('border-color', '--card-border');
  @include themeify('box-shadow', '0 4px 16px var(--card-shadow)');
  @include theme-transition(background, border-color, box-shadow);
  
  &:hover {
    @include themeify('box-shadow', '0 6px 20px var(--card-hover-shadow)');
  }
}

// Standardized focus style
@mixin focus-style {
  outline: none;
  @include themeify('border-color', '--input-focus-border');
  @include themeify('box-shadow', '0 0 0 2px var(--input-focus-shadow)');
} 