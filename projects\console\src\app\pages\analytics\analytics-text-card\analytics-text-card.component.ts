import { Component, Input } from '@angular/core';
import { TxtCardComponent, CardContentComponent, IconComponent } from "@ava/play-comp-library";

@Component({
  selector: 'app-analytics-txt-card',
  imports: [TxtCardComponent, CardContentComponent, IconComponent],
  templateUrl: './analytics-text-card.component.html',
  styleUrl: './analytics-text-card.component.scss'
})
export class AnalyticsTxtCardComponent {
  @Input() title = ""
  @Input() value = 0
  @Input() subtitle = ""
  @Input() iconName = ""

}