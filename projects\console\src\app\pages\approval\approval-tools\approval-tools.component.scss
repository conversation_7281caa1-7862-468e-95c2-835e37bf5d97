/* Loading Overlay Styles */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  
  p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--color-brand-primary);
  }
  
  ava-icon {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.approval {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;
  }
  
  .approval-left-screen {
    flex: 0 0 70px;           /* Fixed width to match your ava-sidebar width */
    width: 70px;
    &.quick-actions-expanded{
      flex: 0 0 250px;
      margin-right: 15px;
    }
    transition: all var(--transition-speed) ease;
    // background-color: #ffffff; /* optional background color */
    height: 120vh;             /* Make it full height if needed */
    overflow: hidden;
  }
  
  /* Right side (main content) */
  .approval-right-screen {
    flex: 1;                   /* Take remaining space */
    padding: 1rem;             /* Some padding for content */
    padding-top: 0px;
    overflow-y: auto;          /* Scrollable if content is long */
    position: relative;        /* For loading overlay positioning */
    height: calc(100vh - 80px); /* Full viewport height minus header */
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  /* Container stacking */
  .approval-title-filter {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem; /* gap below entire header section */
  }
  
  /* Title styling */
  .approvals-title {
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }
  
  /* Filter section layout */
  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1rem;
  }
  
  /* Search bars (left) */
  .search-bars {
    display: flex;
    flex-direction: column;
    gap: 1rem;                  /* space between dropdowns and button */
  }
  
  /* Textbox section (right) */
  .textbox.section {
    margin-left: 1rem;
  }
  
  .textbox.section > div {
    display: flex;
    align-items: center; /* vertically centers them */
    gap: 8px;            /* optional spacing between them */
  }
  
  .approval-card-header {
    font-size: 1.25rem;              /* Slightly larger text */
    font-weight: 600;                /* Semi-bold */
    color: grey;                     /* Dark gray text */
    margin-bottom: 1.5rem;             /* Space below the header */
    display: flex;
    align-items: center;
    justify-content: space-between;  /* In case you later add actions on the right */
  }
  
  
  .approval-title-filter {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    flex-wrap: nowrap;
    width: 100%;
    padding: 1rem 0;
    padding-top: 0px;
  }
  
  .approval-title-filter > ava-text-card {
    flex: 1 1 22%;
    min-width: 200px;
  }
  
  .quick-actions-wrapper {
    grid-area: quick-actions;
    background-color: var(--dashboard-card-bg);
    border-radius: var(--border-radius-standard);
    display: flex;
    flex-direction: column;
    width: 55px;
    height: 250vh;
    transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
    border: var(--border-thin);
    position: relative;
    
    &:hover {
      box-shadow: var(--shadow-hover);
    }
    
    /* Expanded state - width is controlled by parent grid */
    &.expanded {
      width: 100%; /* Use 100% width to fit the grid cell */
    }
  
    @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
      height: 595px;
    }
    
    /* Responsive adjustments */
    @media (min-width: 1200px) and (max-width: 1400px) {
      height: 580px !important;
      max-height: 580px !important;
    }
    
    @media (max-width: 1200px) {
      height: 320px;
    }
    
    @media (max-width: 992px) {
      flex-direction: row;
      width: 100%;
      height: 48px;
      
      &.expanded {
        height: auto;
        max-height: 320px;
        width: 100%;
      }
    }
    
    @media (max-width: 576px) {
      height: 280px;
    }
    
    /* Special case for 13" laptops */
    @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
      height: 100%;
      
      ::ng-deep .card-container {
        height: 100%;
      }
    }
    
    /* Card styling */
    ::ng-deep .quick-actions-card {
      height: 100% !important;
      width: 100% !important;
      
      .card-container {
        height: 100% !important;
        width: 100% !important;
        padding: 0 !important;
        overflow: hidden !important;
        display: flex !important;
        flex-direction: column !important;
        
        .card-body {
          padding: 0 !important;
          height: 100% !important;
          display: flex !important;
          flex-direction: column !important;
        }
      }
      
      .card-content {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        padding: 0 !important;
        
        @media (max-width: 992px) {
          flex-direction: row !important;
        }
      }
      
      /* Responsive card adjustments */
      @media (max-width: 992px) {
        .card-container {
          height: 48px !important;
          width: 100% !important;
          flex-direction: row !important;
          
          &.expanded {
            height: auto !important;
          }
        }
      }
      
      /* Special case for 13" laptops */
      @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
        .card-container {
          width: 100% !important;
        }
      }
    }
  }
  
  .quick-actions-content {
    padding: 20px 16px;
    overflow-y: auto;
    flex-grow: 1;
    
    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 16px; /* Increased gap for better spacing */
      
      .action-button {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 16px; /* Wider gap for better spacing */
        padding: 16px 20px; /* More padding for better touch area */
        border-radius: 12px; /* Rounded corners */
        border: none;
        border: 2px solid transparent;
        background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);
        background-origin: border-box;
        background-clip: padding-box, border-box;
        --button-effect-color: 33, 90, 214;
        cursor: pointer;
        transition: all var(--transition-speed) ease;
        width: 100%;
        text-align: left;
        color: #fff;
  
        .action-icon,
        .action-label {
          color: #fff;
        }
  
        &:hover {
          opacity: 0.9;
          background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px var(--dashboard-shadow-hover);
        }
        
        .action-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          
          img {
            width: 20px;
            height: 20px;
            filter: brightness(0) invert(1); /* Make SVG white */
          }
        }
        
        .action-label {
          font-size: 16px;
          font-weight: 500;
          color: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%); /* Use the new variable */
        }
      }
    }
  }
  
  .action-button.active-action {
    background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;
  }
  
  .quick-actions-toggle {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 16px;
    padding-bottom: 0px;
    cursor: pointer;
    transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    
    /* Adjust the toggle for the collapsed state to center the button */
    .quick-actions-wrapper:not(.expanded) & {
      padding: 20px 0;
      justify-content: center;
    }
    
    .toggle-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 8px;
      background-color: transparent;
      position: relative;
      
      /* Add gradient border for expanded state */
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 8px;
        padding: 1px;
        background: var(--dashboard-gradient);
        -webkit-mask: 
          linear-gradient(#fff 0 0) content-box, 
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      }
      
      svg {
        transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
        width: 16px;
        height: 16px;
        stroke: var(--dashboard-toggle-stroke);
        z-index: 1;
        
        &.rotate {
          transform: rotate(180deg);
        }
      }
    }
    
    /* Special styling for collapsed state to match other buttons */
    .quick-actions-wrapper:not(.expanded) .toggle-button {
      background: var(--dashboard-gradient);
      transition: background 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      
      &::before {
        display: none;
      }
      
      svg {
        stroke: var(--dashboard-toggle-stroke-collapsed);
        transition: stroke 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
    
    span {
      font-weight: 580;
      font-size: 16px;
      color: var(--dashboard-text-primary);
      opacity: 1;
      transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
  
  .quick-actions-icons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px; /* Increased gap between items */
    padding: 20px 0; /* More vertical padding */
    height: 150vh;
    
    @media (max-width: 992px) {
      flex-direction: row;
      justify-content: center;
      flex-wrap: wrap;
      padding: 8px;
    }
    
    .icon-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px; /* Larger button size */
      height: 36px; /* Larger button size */
      border-radius: 8px; /* Slightly rounded corners */
      border: none;
      background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);
      cursor: pointer;
      transition: all var(--transition-speed) ease;
      
      &:hover {
        background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%);
        opacity: 0.9;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--dashboard-shadow-hover);
      }
      
      .icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        
        img {
          width: 20px;
          height: 20px;
          filter: brightness(0) invert(1); /* Make SVG white */
        }
      }
    }
  }
  
  .icon-button.active-action {
    background: linear-gradient(103.35deg, #03BDD4 31.33%, #215AD6 100%) !important;
  }
  
  .approval-card-section{
    display: flex;
    flex-direction: row;
    gap: 16px;
    overflow-y: auto;
    max-height: 310px;
    flex-wrap: wrap;
    &::-webkit-scrollbar {
      display: none;
    }  
    .approval-card-wrapper {
      flex: 0 0 calc(50% - 8px); 
      min-width: 300px; 
    }
  }
  
  .approval-card-wrapper {
    flex: 0 0 auto;
    min-width: 50%;
  }
  
  .no-pending-message {
    display: flex;
    // align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1.2rem;
    color: #000000;
    font-weight: 500;
    text-align: center;
    border-radius: 16px;
    min-height: 100px;
  }
  
  /* Responsive (optional) for mobile screens */
  @media (max-width: 768px) {
    .filter-section {
      flex-direction: column;
      align-items: stretch;
    }
  
    .search-bars,
    .textbox.section {
      width: 100%;
      margin: 0 0 0.5rem 0;
      justify-content: center;
    }
  
    .search-bars {
      flex-wrap: wrap;
      gap: 0.5rem;
    }
  }
  
/* Skeleton Loading Styles */
.skeleton-title,
.skeleton-text,
.skeleton-tag,
.skeleton-icon,
.skeleton-button {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  border-radius: 4px;
}

.skeleton-title {
  height: 24px;
  width: 80%;
  margin-bottom: 8px;
}

.skeleton-text {
  height: 16px;
  width: 60%;
  margin-bottom: 4px;
}

.skeleton-tag {
  height: 24px;
  width: 80px;
  margin-right: 8px;
  margin-bottom: 8px;
  display: inline-block;
}

.skeleton-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.skeleton-button {
  height: 36px;
  width: 80px;
  margin-right: 8px;
  border-radius: 6px;
}

/* Skeleton layout improvements */
.header .skeleton-title {
  width: 70%;
}

.header .skeleton-tag {
  width: 60px;
  height: 20px;
}

.tag-wrapper .skeleton-tag {
  width: 70px;
  height: 22px;
}

.info-wrapper .f,
.info-wrapper .s {
  display: flex;
  align-items: center;
}

.info-wrapper .skeleton-text {
  width: 80px;
}

.footer-left .skeleton-text {
  width: 100px;
  height: 14px;
  margin-bottom: 8px;
}

.footer-left > div > div {
  display: flex;
  align-items: center;
}

.footer-left > div > div .skeleton-text {
  width: 60px;
  margin-left: 4px;
}

.footer-right {
  display: flex;
  gap: 8px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Dark theme skeleton styles */
:host-context(.dark-theme) {
  .skeleton-title,
  .skeleton-text,
  .skeleton-tag,
  .skeleton-icon,
  .skeleton-button {
    background: linear-gradient(
      90deg,
      #333 25%,
      #444 50%,
      #333 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite linear;
  }
}
  