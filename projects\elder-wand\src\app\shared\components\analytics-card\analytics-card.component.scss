.analytics {
 gap: 48px;
}

.analytics-header {
  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    letter-spacing: -0.025em;
  }
}

.analytics-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(139, 92, 246, 0.1);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.05);
  backdrop-filter: blur(8px);
margin-bottom: 24px;
  .section-header {
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #6b7280;
      margin: 0;
    }
  }
}

// Agents Used Today Section
.agents-section {
  .metric-display {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .metric-value {
      font-size: 20px;
      font-weight: 600;
      color: #1f2937;
    }

    .metric-badge {
      background: linear-gradient(90deg, #8B5CF6 0%, #EC4899 100%);
      color: white;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  .chart-container {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    .line-chart {
      width: 100%;
      height: 100%;

      .line-svg {
        width: 100%;
        height: 100%;
      }
    }
  }
}

// Task Achieved Section
.task-section {
  .task-content {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .task-percentage {
    .percentage-circle {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: conic-gradient(
        from 0deg,
        #8B5CF6 0deg 126deg,
        #EC4899 126deg 216deg,
        #F97316 216deg 306deg,
        #10B981 306deg 360deg
      );
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        width: 50px;
        height: 50px;
        background: white;
        border-radius: 50%;
      }

      .percentage-text {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        position: relative;
        z-index: 1;
      }
    }
  }

  .task-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .legend-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }

      .legend-label {
        font-size: 12px;
        color: #6b7280;
        font-weight: 500;
      }
    }
  }
}

// Time Saved Section
.time-section {
  .time-display {
    margin-bottom: 16px;

    .time-value {
      font-size: 32px;
      font-weight: 600;
      color: #1f2937;
      line-height: 1;
      margin-bottom: 4px;
    }

    .time-subtitle {
      font-size: 14px;
      color: #6b7280;
      font-weight: 500;
    }
  }

  .chart-container {
    height: 60px;
    display: flex;
    align-items: end;
    justify-content: center;

    .bar-chart {
      display: flex;
      align-items: end;
      gap: 4px;
      height: 100%;
      width: 100%;
      justify-content: center;

      .bar-item {
        width: 8px;
        background: linear-gradient(180deg, #8B5CF6 0%, #EC4899 100%);
        border-radius: 2px 2px 0 0;
        min-height: 4px;
        transition: all 0.3s ease;

        &:nth-child(2n) {
          background: linear-gradient(180deg, #EC4899 0%, #F97316 100%);
        }

        &:nth-child(3n) {
          background: linear-gradient(180deg, #F97316 0%, #10B981 100%);
        }

        &:nth-child(4n) {
          background: linear-gradient(180deg, #10B981 0%, #8B5CF6 100%);
        }
      }
    }
  }
}

// Dark theme support
.dark-theme .analytics-container {
  background: linear-gradient(102deg, rgba(30, 30, 35, 0.7) 1.07%, rgba(40, 40, 45, 0.7) 98.01%);
  border: 1px solid rgba(80, 80, 95, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(139, 92, 246, 0.1);

  &::before {
    background: linear-gradient(135deg, 
      rgba(139, 92, 246, 0.05) 0%, 
      rgba(236, 72, 153, 0.05) 50%, 
      rgba(30, 30, 35, 0.95) 100%);
  }

  .analytics-header h2 {
    color: #e0e0e0;
  }

  .analytics-section {
    background: rgba(40, 40, 50, 0.6);
    border: 1px solid rgba(80, 80, 95, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

    .section-header h3 {
      color: #a0a0a0;
    }
  }

  .agents-section .metric-display .metric-value,
  .task-section .percentage-text,
  .time-section .time-value {
    color: #e0e0e0;
  }

  .task-section .task-legend .legend-label,
  .time-section .time-subtitle {
    color: #a0a0a0;
  }

  .task-section .percentage-circle::before {
    background: rgba(40, 40, 50, 0.9);
  }
}
