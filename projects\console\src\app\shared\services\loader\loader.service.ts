import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LoaderService {
  private isLoadingSubject = new BehaviorSubject<boolean>(false);
  private inFlightHttpCalls: string[] = [];
  public loaderPull: boolean = true;
  isLoadedEnabled = true;

  /**
   * Observable to track loading state
   */
  get isLoading$(): Observable<boolean> {
    return this.isLoadingSubject.asObservable();
  }

  /**
   * Get current loading state
   */
  get isLoading(): boolean {
    return this.isLoadingSubject.value;
  }

  /**
   * Show loader
   */
  show(): void {
    if (this.isLoadedEnabled) {
      this.isLoadingSubject.next(true);
    }
  }

  /**
   * Hide loader
   */
  hide(): void {
    this.isLoadingSubject.next(false);
  }

  /**
   * Add an in-flight HTTP request by unique ID
   */
  private addInflightHttpRequest(requestId: string): void {
    if (!this.isAnyHttpRequestPending()) {
      // Show loader when first http request starts
      this.show();
    }
    this.inFlightHttpCalls.push(requestId);
  }

  /**
   * Remove a completed HTTP request by unique ID
   */
  private removeCompletedHttpRequest(requestId: string): void {
    const index = this.inFlightHttpCalls.indexOf(requestId);
    if (index > -1) {
      this.inFlightHttpCalls.splice(index, 1);
    }
    // Hide the loader only if all the requests are completed
    if (!this.isAnyHttpRequestPending()) {
      this.hide();
    }
  }

  /**
   * Check if any HTTP request is pending
   */
  private isAnyHttpRequestPending(): boolean {
    return this.inFlightHttpCalls.length > 0;
  }

  /**
   * Service started - called when a request begins
   */
  serviceStarted(requestId: string): void {
    if (this.loaderPull) {
      this.addInflightHttpRequest(requestId);
    }
  }

  /**
   * Service completed - called when a request ends
   */
  serviceCompleted(requestId: string): void {
    this.removeCompletedHttpRequest(requestId);
  }

  /**
   * Reset loader state (useful for error handling or navigation)
   */
  reset(): void {
    this.inFlightHttpCalls = [];
    this.isLoadingSubject.next(false);
  }

  /**
   * Call this on navigation events to ensure loader is reset
   */
  resetOnNavigation(): void {
    this.reset();
  }

  disableLoader() {
    this.isLoadedEnabled = false;
  }
  enableLoader() {
    this.isLoadedEnabled = true;
  }
}
