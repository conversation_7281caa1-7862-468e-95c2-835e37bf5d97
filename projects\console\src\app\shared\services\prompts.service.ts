import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { catchError, map, Observable, of } from 'rxjs';
import { CardData } from '../../shared/models/card.model';

@Injectable({
  providedIn: 'root'
})

export class PromptsService {
  private apiServiceUrl = environment.consoleApi;
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    })
  };
  private http = inject(HttpClient); //  Injecting HttpClient

  constructor() { }

  /**
   * Deletes a prompt by ID
   * @param promptId - The ID of the prompt to delete
   * @returns Observable<any>
   */
  deletePrompt(promptId: string | number): Observable<any> {
    const url = `${this.apiServiceUrl}/ava/force/prompts?promptId=${promptId}`;
    return this.http.delete(url, this.headers).pipe(
      catchError((error: any) => {
        console.error('API error while deleting prompt:', error);
        return of(null);
      })
    );
  }

  /**
   * Fetches all prompt data from the API
   *
   * @returns Observable emitting an array of CardData items
   * - On success: returns the fetched data
   * - On error: logs the error and returns an empty array
   */
  fetchAllPrompts(): Observable<CardData[]> {
    const url = `${this.apiServiceUrl}/ava/force/prompts`;
    return this.http.get<any>(
      url, this.headers  //  Pass headers correctly
    ).pipe(
      map((response: any) => {
        // Handle both array response and object with prompts property
        if (Array.isArray(response)) {
          return response;
        } else if (response && Array.isArray(response.prompts)) {
          return response.prompts;
        } else {
          console.warn('Unexpected API response structure:', response);
          return [];
        }
      }),
      catchError((error: any) => {
        console.error('API error:', error); //  Log the API error
        return of([]); //  Fallback: return an empty array on error
      })
    );
  }

  /**
   * Fetches a specific prompt by ID
   * @param promptId - The ID of the prompt to fetch
   * @returns Observable emitting the prompt data
   */
  getPromptById(promptId: string | number): Observable<any> {
    const url = `${this.apiServiceUrl}/ava/force/prompts?promptId=${promptId}`;
    return this.http.get<any>(url, this.headers).pipe(
      map((response: any) => {
        return response.prompt;
      }),
      catchError((error: any) => {
        console.error('API error fetching prompt by ID:', error);
        return of(null);
      })
    );
  }
  

  /**
 * Adds a new prompt
 * @param promptData - The full prompt object to be submitted
 * @returns Observable<any>
 */
addPrompt(promptData: any): Observable<any> {
  const url = `${this.apiServiceUrl}/ava/force/prompts`;
  // Build payload based on type
  let payload: any = {};
  const type = (promptData.type || '').toLowerCase();
  if (type === 'zero shot') {
    payload = {
      type: promptData.type,
      name: promptData.name,
      role: promptData.role,
      goal: promptData.goal,
      backstory: promptData.backstory,
      description: promptData.description,
      promptDescription: promptData.promptDescription,
      expectedOutput: promptData.expectedOutput,
      prompt: promptData.prompt
    };
  } else if (type === 'one shot/multi shot' || type === 'one shot' || type === 'multi shot') {
    payload = {
      type: promptData.type,
      name: promptData.name,
      role: promptData.role,
      goal: promptData.goal,
      backstory: promptData.backstory,
      description: promptData.description,
      promptDescription: promptData.promptDescription,
      expectedOutput: promptData.expectedOutput,
      prompt: promptData.prompt,
      examples: promptData.examples,
      additionalConsideration: promptData.additionalConsideration
    };
  } else if (type === 'chain of thought') {
    payload = {
      type: promptData.type,
      name: promptData.name,
      role: promptData.role,
      goal: promptData.goal,
      backstory: promptData.backstory,
      description: promptData.description,
      promptDescription: promptData.promptDescription,
      expectedOutput: promptData.expectedOutput,
      prompt: promptData.prompt,
      intermediateSteps: promptData.intermediateSteps,
      examples: promptData.examples,
      additionalConsideration: promptData.additionalConsideration
    };
  } else if (type === 'free form') {
    // Explicitly handle free form: only send type, name, and prompt
    payload = {
      type: promptData.type,
      name: promptData.name,
      prompt: promptData.prompt,
      promptDescription: promptData.promptDescription
    };
  } else {
    payload = promptData;
  }
  return this.http.post(url, payload, this.headers).pipe(
    catchError((error: any) => {
      console.error('API error while adding prompt:', error);
      return of(null);
    })
  );
}
  
  /**
   * Fetches dropdown values for prompt type from the API
   * @returns Observable<{ name: string, value: string }[]>
   */
  fetchPromptTypeDropdown(type:string): Observable<{ name: string, value: string }[]> {
    const url = `${this.apiServiceUrl}/ava/force/refdata?ref_key=${type}`;
    return this.http.get<any>(url, this.headers).pipe(
      map((response: any) => {
        // The value is a JSON string, parse it
        if (response && response.value) {

          try {
            const parsed = JSON.parse(response.value);
            // parsed is an object: { "Zero Shot": "Zero Shot", ... }
            return Object.entries(parsed).map(([key, val]) => ({ name: val as string, value: key as string }));
          } catch (e) {
            console.error('Failed to parse prompt type dropdown values:', e);
            return [];
          }
        }
        return [];
      }),
      catchError((error: any) => {
        console.error('API error fetching prompt type dropdown:', error);
        return of([]);
      })
    );
  }

/**
 * Edits an existing prompt
 * @param updatedPrompt - The updated prompt object including its ID
 * @returns Observable<any>
 */
editPrompt(updatedPrompt: {
  id: number | string;
  categoryId: number;
  type: string;
  name: string;
  prompt: string;
}): Observable<any> {
  const url = `${this.apiServiceUrl}/ava/force/prompts`;

  return this.http.put(url, updatedPrompt, this.headers).pipe(
    catchError((error: any) => {
      console.error('API error while editing prompt:', error);
      return of(null);
    })
  );
}

/**
 * Sends prompt details to preview the expected output without saving.
 * @param payload - The prompt details for preview
 * @returns Observable<any>
 */
previewPrompt(payload: {
  role: string;
  goal: string;
  backstory: string;
  description: string;

  expectedOutput: string;
}): Observable<any> {
  const url = `${this.apiServiceUrl}/ava/force/prompts/preview`;

  return this.http.post(url, payload, this.headers).pipe(
    catchError((error: any) => {
      console.error('API error while previewing prompt:', error);
      return of(null);
    })
  );
}
}
