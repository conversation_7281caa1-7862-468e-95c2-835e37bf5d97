import { Injectable, ComponentRef, ViewContainerRef, ApplicationRef, Injector, createComponent, EnvironmentInjector } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { AgentDetailsPanelComponent } from '../components/agent-details-panel/agent-details-panel.component';

export interface DrawerConfig {
  data?: any;
  width?: string;
  height?: string;
  position?: 'right' | 'left' | 'top' | 'bottom';
  backdrop?: boolean;
  closeOnBackdropClick?: boolean;
  closeOnEscape?: boolean;
  onGoToPlayground?: (agent: any) => void;
}

@Injectable({
  providedIn: 'root'
})
export class DrawerService {
  private isOpenSubject = new BehaviorSubject<boolean>(false);
  private configSubject = new BehaviorSubject<DrawerConfig | null>(null);
  private overlayElement: HTMLElement | null = null;
  private drawerElement: HTMLElement | null = null;
  private componentRef: ComponentRef<AgentDetailsPanelComponent> | null = null;

  public isOpen$: Observable<boolean> = this.isOpenSubject.asObservable();
  public config$: Observable<DrawerConfig | null> = this.configSubject.asObservable();

  constructor(
    private appRef: ApplicationRef,
    private injector: Injector
  ) {
    // Listen for escape key to close drawer
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && this.isOpenSubject.value) {
        const config = this.configSubject.value;
        if (config?.closeOnEscape !== false) {
          this.close();
        }
      }
    });
  }

  /**
   * Opens the drawer with the specified configuration
   * @param config Drawer configuration
   */
  open(config: DrawerConfig): void {
    // Close existing drawer if open
    if (this.isOpenSubject.value) {
      this.close();
    }

    const finalConfig: DrawerConfig = {
      width: '600px',
      height: '100%',
      position: 'right' as const,
      backdrop: true,
      closeOnBackdropClick: true,
      closeOnEscape: true,
      ...config
    };

    this.configSubject.next(finalConfig);
    this.createDrawerElements(finalConfig);
    this.isOpenSubject.next(true);

    // Prevent body scrolling when drawer is open
    document.body.classList.add('drawer-open');
  }

  /**
   * Closes the drawer
   */
  close(): void {
    this.isOpenSubject.next(false);
    this.configSubject.next(null);

    // Clean up DOM elements
    this.destroyDrawerElements();

    // Re-enable body scrolling
    document.body.classList.remove('drawer-open');
  }

  /**
   * Gets the current drawer state
   */
  get isOpen(): boolean {
    return this.isOpenSubject.value;
  }

  /**
   * Gets the current drawer configuration
   */
  get config(): DrawerConfig | null {
    return this.configSubject.value;
  }

  /**
   * Creates the drawer overlay and content elements
   */
  private createDrawerElements(config: DrawerConfig): void {
    // Create overlay
    this.overlayElement = document.createElement('div');
    this.overlayElement.className = 'drawer-overlay';
    this.overlayElement.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(8px);
      z-index: 1000;
      display: flex;
      justify-content: ${config.position === 'left' ? 'flex-start' : 'flex-end'};
      animation: fadeIn 0.3s ease;
    `;

    // Create drawer content container
    this.drawerElement = document.createElement('div');
    this.drawerElement.className = `drawer-content drawer-${config.position}`;
    this.drawerElement.style.cssText = `
      width: ${config.width};
      height: ${config.height};
      background-color: #fff;
      box-shadow: ${config.position === 'left' ? '4px 0 24px rgba(0, 0, 0, 0.1)' : '-4px 0 24px rgba(0, 0, 0, 0.1)'};
      overflow: hidden;
      display: flex;
      flex-direction: column;
      animation: slideIn${config.position === 'left' ? 'Left' : 'Right'} 0.3s ease;
    `;

    // Add click handlers
    if (config.closeOnBackdropClick !== false) {
      this.overlayElement.addEventListener('click', (event) => {
        if (event.target === this.overlayElement) {
          this.close();
        }
      });
    }

    this.drawerElement.addEventListener('click', (event) => {
      event.stopPropagation();
    });

    // Create and attach the component
    this.componentRef = createComponent(AgentDetailsPanelComponent, {
      environmentInjector: this.appRef.injector
    });

    // Set component inputs
    this.componentRef.instance.agent = config.data;
    this.componentRef.instance.close.subscribe(() => this.close());
    this.componentRef.instance.goToPlayground.subscribe((agent) => {
      if (config.onGoToPlayground) {
        config.onGoToPlayground(agent);
      }
    });

    // Append component to drawer
    this.drawerElement.appendChild(this.componentRef.location.nativeElement);
    this.overlayElement.appendChild(this.drawerElement);
    document.body.appendChild(this.overlayElement);

    // Attach to application
    this.appRef.attachView(this.componentRef.hostView);

    // Add CSS animations
    this.addAnimationStyles();
  }

  /**
   * Destroys the drawer elements and cleans up
   */
  private destroyDrawerElements(): void {
    if (this.componentRef) {
      this.appRef.detachView(this.componentRef.hostView);
      this.componentRef.destroy();
      this.componentRef = null;
    }

    if (this.overlayElement) {
      document.body.removeChild(this.overlayElement);
      this.overlayElement = null;
    }

    this.drawerElement = null;
  }

  /**
   * Adds CSS animation styles to the document
   */
  private addAnimationStyles(): void {
    const styleId = 'drawer-animations';
    if (document.getElementById(styleId)) {
      return;
    }

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      @keyframes slideInRight {
        from { transform: translateX(100%); }
        to { transform: translateX(0); }
      }

      @keyframes slideInLeft {
        from { transform: translateX(-100%); }
        to { transform: translateX(0); }
      }

      body.drawer-open {
        overflow: hidden;
      }
    `;
    document.head.appendChild(style);
  }
}
