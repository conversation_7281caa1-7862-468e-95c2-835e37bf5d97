import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Subject, Observable, of } from 'rxjs';
import { debounceTime, switchMap, catchError, shareReplay } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

type ModuleType = 'agents' | 'workflows' | 'tools';
type FilterType = 'individual' | 'collaborative' | 'default';

@Injectable({
  providedIn: 'root',
})
export class DebouncedSearchService {
  private consoleApiV1 = environment.consoleApi;
  private consoleApiV2 = environment.consoleApiV2;

  private searchSubject = new Subject<{
    query: string;
    moduleType: ModuleType;
    filterType?: FilterType;
  }>();

  // Map of API endpoints for each module type and filter type
  private readonly apiMap: Record<ModuleType, Partial<Record<FilterType, string>>> = {
    agents: {
      individual: `${this.consoleApiV1}/ava/force/individualAgents?search=`,
      collaborative: `${this.consoleApiV2}/ava/force/da/agent?isDeleted=false&search=`,
      default: '',
    },
    workflows: {
      default: `${this.consoleApiV2}/ava/force/da/workflow?isDeleted=false&search=`,
    },
    tools: {
      default: `${this.consoleApiV2}/ava/force/da/userTools?isDeleted=false&search=`,
    },
  };

  // Observable that emits debounced search results based on input query and module/filter type
  searchResults$: Observable<any> = this.searchSubject.asObservable().pipe(
    debounceTime(300), // Wait 300ms before firing search
    switchMap(({ query, moduleType, filterType = 'default' }) => {
      // Select the correct API URL based on module and filter type
      const moduleFilters = this.apiMap[moduleType];
      const validFilterType = filterType in moduleFilters ? filterType : 'default';

      const apiUrl = moduleFilters[validFilterType];
      const fullUrl = `${apiUrl}${encodeURIComponent(query.trim())}`;
      //const fullUrl = `${apiUrl}${query.trim().replace(/\s+/g, '')}`;


      // Make the HTTP GET request and catch errors
      return this.http.get<any>(fullUrl).pipe(
        catchError((error) => {
          console.error('[Search Error]', error);
          return of([]);
        })
      );
    }),
    shareReplay(1) // Share the latest value among subscribers
  );

  constructor(private http: HttpClient) {}

  /**
   * Triggers a new search with the given query, module, and filter type.
   * @param query The search keyword
   * @param moduleType The module type (e.g., 'agents', 'workflows')
   * @param filterType Optional filter type ('individual', 'collaborative', etc.)
   */
  triggerSearch(
    query: string,
    moduleType: ModuleType,
    filterType: FilterType = 'default'
  ): void {
    this.searchSubject.next({ query, moduleType, filterType });
  }

  /**
   * Returns the full API URL based on the given module and filter type.
   * @param moduleType The module type (e.g., 'agents', 'workflows')
   * @param filterType Optional filter type
   * @returns Full API URL as a string
   */
  getApiUrl(moduleType: ModuleType, filterType: FilterType = 'default'): string {
    const moduleFilters = this.apiMap[moduleType];
    const validFilterType = filterType in moduleFilters ? filterType : 'default';
    return moduleFilters[validFilterType] || '';
  }
}
