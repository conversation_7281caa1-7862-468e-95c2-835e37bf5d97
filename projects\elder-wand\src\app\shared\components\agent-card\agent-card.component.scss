// Keyframes for shimmer animation
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// Base card styles
.agent-card {
  background: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: #e0e0e0;
  }

  // Marketplace variant
  &.marketplace-card {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 2px solid #fff;
    padding: 24px;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
  }

  // Dashboard variant
  &.dashboard-card {
    background: #fff;
    border: 1px solid #f0f0f0;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      border-color: #e0e0e0;
    }
  }

  // Loading state
  &.loading {
    cursor: default;

    &:hover {
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }
  }
}

// Card content
.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
}

// Header styles
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;

  &.marketplace-header {
    .agent-title-truncate {
      flex: 1;
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
      margin: 0;
      line-height: 1.4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100% - 80px);
    }

    .rating {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
      font-weight: 500;
      color: #616874;
      flex-shrink: 0;
    }
  }

  &.dashboard-header {
    .agent-title {
      flex: 1;
      font-size: 16px;
      font-weight: 600;
      color: var(--Colors-Text-secondary, #616874);
      margin: 0;
      line-height: 1.4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100% - 80px);
    }

    .status-badge {
      flex-shrink: 0;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
      text-transform: capitalize;

      &.status-approved {
        color: #059669;
        background-color: #ecfdf5;
      }

      &.status-pending {
        color: #616874;
        background-color: #f3f4f6;
      }

      &.status-denied {
        color: #dc2626;
        background-color: #fef2f2;
      }

      &.status-draft {
        color: #dc2626;
        background-color: #fef2f2;
      }
    }
  }
}

// Description styles
.agent-description {
  flex: 1;
  font-size: 14px;
  color: #616874;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Footer styles
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  gap: 16px;

  &.marketplace-footer {
    .users {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 14px;
      color: #858aad;
    }

    .agent-time-ago {
      font-size: 12px;
      color: #858aad;
    }
  }

  &.dashboard-footer {
    .meta-info {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;

      .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .meta-text {
          font-size: 12px;
          color: var(--Colors-Text-secondary, #616874);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      flex-shrink: 0;

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 8px;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f3f4f6;
          transform: scale(1.05);
        }

        &.primary {
          background-color: #e91e63;
          color: white;

          &:hover {
            background-color: #d81b60;
            transform: scale(1.05);
          }
        }
      }
    }
  }
}

// Skeleton styles
.skeleton-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 24px;
  position: relative;
  z-index: 1;
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;

  .skeleton-title {
    flex: 1;
    height: 20px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
  }

  .skeleton-rating {
    display: flex;
    align-items: center;
    gap: 6px;

    .skeleton-star {
      width: 18px;
      height: 18px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 2px;
    }

    .skeleton-rating-text {
      width: 30px;
      height: 16px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 3px;
    }
  }

  .skeleton-status {
    width: 60px;
    height: 24px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 12px;
  }
}

.skeleton-description {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  height: 60px;
  overflow: hidden;

  .skeleton-line {
    height: 14px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 3px;

    &.short {
      width: 60%;
    }
  }
}

.skeleton-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;

  .skeleton-user {
    display: flex;
    align-items: center;
    gap: 4px;

    .skeleton-icon {
      width: 16px;
      height: 16px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 50%;
    }

    .skeleton-user-text {
      width: 80px;
      height: 16px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 3px;
    }
  }

  .skeleton-meta {
    .skeleton-date {
      width: 100px;
      height: 12px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 3px;
    }
  }

  .skeleton-time {
    width: 60px;
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 3px;
  }

  .skeleton-actions {
    display: flex;
    gap: 8px;

    .skeleton-action-btn {
      width: 32px;
      height: 32px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200px 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 8px;

      &.primary {
        background: linear-gradient(
          90deg,
          #2f5a8e40 25%,
          #1a46a740 50%,
          #2f5a8e40 75%
        );
        background-size: 200px 100%;
      }
    }
  }
}

// Tooltip styling for truncated text
[data-tooltip] {
  position: relative;
  cursor: help;
}

[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: normal;
  max-width: 250px;
  word-wrap: break-word;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  animation: tooltip-fade-in 0.2s ease-in-out forwards;
}

[data-tooltip]:hover::before {
  content: "";
  position: absolute;
  bottom: calc(100% - 6px);
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  animation: tooltip-fade-in 0.2s ease-in-out forwards;
}
