import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { of, map } from 'rxjs';
import { formatDate } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class SharedApiServiceService {
  private baseUrl = environment.consoleApi;
  private embeddingUrl = environment.consoleEmbeddingApi;
  private configData: any = null;

  private readonly HttpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  };
  constructor(private http: HttpClient) {}

  public getConfigLabels(chatbot: boolean = false) {
    const headers = this.HttpOptions;
    const url = `${this.baseUrl}/ava/force/label`;
    let param = new HttpParams();
    if (chatbot) {
      param = param.append('chatBot', chatbot);
    }
    if (this.configData) {
      return of(this.configData);
    } else {
      return this.http
        .get(url, { params: param, headers: headers.headers })
        .pipe(
          map((response: any) => {
            this.configData = response;
            return response;
          }),
        );
    }
  }

  public getExistingKnowledgeBaase() {
    const headers = this.HttpOptions;
    const url = `${this.embeddingUrl}/ava/force/knowledge`;
    return this.http.get(url, headers);
  }

  formatDate(date: string | Date, format = 'dd-MM-yyyy') {
    return formatDate(date, format, 'en-US');
  }

  public getCollabrativeAgentAnalytics(dateStart: string, dateEnd: string) {
    const url = `${this.baseUrl}/ava/force/analytics/agenticAI`;
    return this.http.get(url, {
      ...this.HttpOptions,
      params: { dateStart, dateEnd },
    });
  }
}
