<div id="my__account--container" class="my-agent-home-container">
  <div class="header-section">
    <!-- Left side - User Profile -->
    <div class="user-profile-section">
      <div class="profile-trigger">
        <img [src]="userAvatar" alt="User Profile" class="profile-avatar" />
      </div>
      <div class="user-details">
        <h2 class="user-name">{{ userName }}</h2>
        <p class="user-email">{{ userEmail }}</p>
        <p class="user-role">{{ userRole }}</p>
      </div>
    </div>

    <!-- Right side - Action Buttons -->
    <div class="action-buttons">
      <ava-button
        class="create-agent-btn"
        label="Create Agent"
        iconName="plus"
        iconPosition="left"
        variant="primary"
        iconColor="#fff"
        (userClick)="onCreateAgent()"
      ></ava-button>
      <ava-button
        label="View Analytics"
        iconName="chart-spline"
        iconPosition="left"
        variant="secondary"
        iconColor="#E91E63"
        [customStyles]="{
          background: 'white',
        }"
        [disabled]="true"
        (userClick)="onViewAnalytics()"
      ></ava-button>
    </div>
  </div>

  <!-- My Agents Section with Tabs -->
  <div class="my-agents-section">
    <h2 class="section-title">My Agents</h2>
    <app-filter-tabs
      [tabs]="agentTabs"
      [activeTab]="activeTab"
      (tabChange)="onTabChange($event)"
    >
    </app-filter-tabs>

    <!-- Agent Cards Grid -->
    <div class="agents-grid row g-3">
      <!-- Loading skeleton cards -->
      <div
        [ngClass]="
          showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'
        "
        *ngFor="let skeleton of getSkeletonCards()"
        [hidden]="!isLoading"
      >
        <app-agent-card
          [isLoading]="true"
          [variant]="isMarketplace ? 'marketplace' : 'dashboard'"
        ></app-agent-card>
      </div>

      <!-- Actual agent cards -->
      <div
        [ngClass]="
          showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'
        "
        *ngFor="let agent of allAgentCards"
        [hidden]="isLoading"
      >
        <app-agent-card
          [agent]="agent"
          [isLoading]="false"
          [variant]="isMarketplace ? 'marketplace' : 'dashboard'"
          (cardClick)="onAgentClick($event)"
          (actionClick)="onAgentAction($event)"
        ></app-agent-card>
      </div>
    </div>
  </div>

  <!-- Pagination Footer -->
  <div class="pagination" *ngIf="shouldShowPagination">
    <div class="col-12 d-flex justify-content-center p-0 m-0">
      <app-page-footer
        [totalItems]="totalItem()"
        [currentPage]="currentPage()"
        [itemsPerPage]="itemsPerPage()"
        (pageChange)="onPageChange($event)"
      ></app-page-footer>
    </div>
  </div>
</div>
