.studios-container {
  padding: 0px 16px;

  .studios-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px; // Significantly reduced from 24px
    margin: 0;

    .studio-card {
      background:
        linear-gradient(
          0deg,
          rgba(255, 255, 255, 0.4) 0%,
          rgba(255, 255, 255, 0.4) 100%
        ),
        linear-gradient(
          102deg,
          var(--Global-colors-Purple-p---50, #f5e9f7) 2.29%,
          #f4f3f3 97.71%
        );
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      border: 2px solid #fff;
      backdrop-filter: blur(12px);
      height: auto;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      outline: none;

      &:hover {
        box-shadow: var(--Elevation-01X) var(--Elevation-01Y)
          var(--Elevation-01Blur) var(--Elevation-01Spread)
          var(--BrandNeutraln-100);
        transform: scale(1.03) translateY(-5px); // pop and lift effect
      }

      &:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
      }

      &:active {
        transform: scale(0.98) translateY(-2px);
      }

      // Ensure the entire card is clickable
      &[role="button"] {
        display: block;
        text-decoration: none;
        color: inherit;

        &:visited {
          color: inherit;
        }
      }

      // Loading state
      &.navigating {
        pointer-events: none;
        opacity: 0.8;
      }

      // Loading overlay
      .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        border-radius: 12px;

        .loading-spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #e5e7eb;
          border-top: 3px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }

      // Skeleton loader styles
      &.skeleton-card {
        pointer-events: none;

        .skeleton-title {
          height: 32px;
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 6px;
          margin-bottom: 12px;
          width: 80%;
        }

        .skeleton-description {
          height: 16px;
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 4px;
          width: 100%;
        }

        .skeleton-image {
          width: 223px;
          height: 195px;
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 8px;
        }

        .skeleton-arrow {
          width: 32px;
          height: 32px;
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 50%;
        }
      }

      .card-content {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 24px 24px 16px 24px;
        position: relative;

        .card-header {
          margin-bottom: 24px;

          h2 {
            color: #1d1d1d;

            font-size: 32px;
            font-weight: 700;
            margin: 0 0 12px 0;
          }

          .description {
            color: #6b7280;

            font-size: 16px;
            font-weight: 500;
            text-align: left;
            margin: 0;
          }
        }

        .card-body {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 20px;
        }

        .card-visual {
          display: flex;
          align-items: center;
          justify-content: flex-start;

          img {
            width: 223px;
            height: 195px;
            object-fit: contain;
          }
        }

        .card-footer {
          position: fixed;
          bottom: 29px;
          right: 16px;
          display: flex;
          align-items: baseline;
          justify-content: center;

          .arrow-button {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            border: 1px solid #e5e7eb;
            background: #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
