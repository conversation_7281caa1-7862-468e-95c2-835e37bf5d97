import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

/**
 * ThemeService manages the application's theme state
 * Provides methods for getting, setting, and toggling themes
 * Persists theme preference in localStorage
 */
@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly themeKey = 'console-theme'; // Key for localStorage
  private themeSubject = new BehaviorSubject<'light' | 'dark'>(this.loadInitialTheme());
  themeObservable = this.themeSubject.asObservable();
  private renderer: Renderer2;
  
  // Use a cached value to avoid accessing localStorage too often
  private cachedTheme: 'light' | 'dark';

  constructor(rendererFactory: RendererFactory2) {
    this.renderer = rendererFactory.createRenderer(null, null);
    this.cachedTheme = this.loadInitialTheme();
    this.setTheme(this.cachedTheme);
    
    // Add document level class for better CSS transitions
    this.renderer.addClass(document.documentElement, 'theme-transition');
  }

  /**
   * Apply a specific theme
   * @param theme The theme to apply ('light' or 'dark')
   */
  setTheme(theme: 'light' | 'dark'): void {
    // Skip if theme hasn't changed
    if (this.cachedTheme === theme && document.body.classList.contains('dark-theme') === (theme === 'dark')) {
      return;
    }
    
    // Use requestAnimationFrame for smoother transitions
    requestAnimationFrame(() => {
      // Remove existing theme classes
      this.renderer.removeClass(document.body, 'light-theme');
      this.renderer.removeClass(document.body, 'dark-theme');
      
      // For the default light theme, we don't add a specific class
      // Only the dark theme gets a class, as light theme variables are in :root
      if (theme === 'dark') {
        this.renderer.addClass(document.body, 'dark-theme');
      }
  
      // Store theme preference (do this async to avoid layout thrashing)
      setTimeout(() => {
        localStorage.setItem(this.themeKey, theme);
      }, 0);
      
      // Update cached theme
      this.cachedTheme = theme;
      
      // Notify subscribers
      this.themeSubject.next(theme);
    });
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme(): void {
    const newTheme = this.getCurrentTheme() === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  }

  /**
   * Get the current applied theme
   * @returns The current theme ('light' or 'dark')
   */
  getCurrentTheme(): 'light' | 'dark' {
    return this.cachedTheme;
  }

  /**
   * Get the appropriate asset path based on current theme
   * @param basePath The base asset path without theme suffix
   * @param extension The file extension (e.g., '.svg', '.png')
   * @returns The themed asset path
   */
  getThemedAssetPath(basePath: string, extension: string): string {
    const theme = this.getCurrentTheme();
    return `${basePath}-${theme}${extension}`;
  }

  /**
   * Load the theme from localStorage when app starts
   * @returns The saved theme or 'light' as default
   */
  private loadInitialTheme(): 'light' | 'dark' {
    const savedTheme = localStorage.getItem(this.themeKey) as 'light' | 'dark';
    return savedTheme || 'light'; // Default to light theme
  }
} 