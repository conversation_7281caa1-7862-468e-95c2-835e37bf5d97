:host {
  padding: 0 1rem;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.user__management--header {
  margin: 0 0 1rem 0;
  display: flex;
  gap: 8px;
}

h2,
h3 {
  color: #4c515b;
}

.basic__info--section {
  display: flex;
  gap: 24px;
}

.filter-label {
  margin-bottom: 4px;
}

.input {
  width: 412px;
}

.dropdown {
  width: 336px;
}

.access__control--container {
  margin-top: 32px;
}

.access__control--section {
  display: flex;
  border-bottom: 1px solid #bbbec5;
  gap: 16px;
  align-items: center;
  padding: 24px 0 20px 0;

  .title {
    font-size: 18px;
    font-weight: 500;
    font-family: Mulish;
    width: 180px;
    color: #000000;
  }
}

.access__control--section:first-of-type {
  padding: 0 0 20px 0;
}

.tag--container {
  display: flex;
  gap: 10px;
}

.dropdown-tag {
  padding: 0 0 0 40px;
}

.realm-container {
  margin-top: 46px;
}

.realm-section {
  height: 218px;
  border: 1px solid #bbbec5;
  border-radius: 1rem;
  padding: 1rem;
  margin: 24px 0 0 0;

  .realm__filter--section {
    display: flex;
    gap: 10px;

    ava-autocomplete {
      flex: 1;
    }
  }

  .realm {
    margin: 24px 0 0 0;
  }
}

.title-sm {
  font-size: 14px;
  color: #898e99;
  font-weight: 600;
}

.realm__form {
  display: flex;
  flex-flow: column;
  gap: 16px;
}

.form-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.input__field--wrapper {
  flex: 1;
  width: 360px;
}

.filter-label {
  display: block;
  font-weight: 500;
  text-align: left;
  color: #14161f;
}

.required::after {
  content: " *";
  color: red;
  font-weight: bold;
}

.button__container {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin: 16px 0 16px 0;

  &.main {
    justify-content: flex-end;
  }
}

.role__form-fields {
  display: flex;
  flex-flow: column;
  gap: 16px;
}

/* Skeleton Loader Styles */
.skeleton-loader {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite ease-in-out;
  border-radius: 8px;
}

.skeleton-input {
  height: 42px;
  width: 100%;
  margin-bottom: 8px;
}

.skeleton-dropdown {
  height: 42px;
  width: 100%;
  margin-bottom: 8px;
}

.skeleton-label {
  height: 16px;
  width: 120px;
  margin-bottom: 8px;
}

.skeleton-access-control {
  margin-bottom: 24px;

  .skeleton-title {
    height: 20px;
    width: 150px;
    margin-bottom: 16px;
  }

  .skeleton-section {
    margin-bottom: 16px;

    .skeleton-section-label {
      height: 16px;
      width: 100px;
      margin-bottom: 8px;
    }
  }
}

.skeleton-realm-section {
  .skeleton-autocomplete {
    height: 42px;
    width: 100%;
    margin-bottom: 12px;
  }

  .skeleton-button {
    height: 42px;
    width: 100%;
    margin-bottom: 16px;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
