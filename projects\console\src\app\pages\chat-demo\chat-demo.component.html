<div class="chat-demo-container">
  <div class="demo-header">
    <h1>Chat Interface Demo</h1>
    <button class="reset-button" (click)="resetChat()">Reset Chat</button>
  </div>
  
  <div class="chat-wrapper">
    <app-chat-interface
      [placeholder]="placeholder"
      [messages]="chatMessages"
      [isLoading]="isLoading"
      (messageSent)="onMessageSent($event)"
      (attachmentClicked)="onAttachmentClicked()">
    </app-chat-interface>
  </div>
  
  <div class="demo-info">
    <h2>Component Information</h2>
    <p>This is a reusable chat interface component that can be used throughout the application. It provides:</p>
    <ul>
      <li>Clean, modern messaging UI</li>
      <li>Support for user and AI messages with distinct styling</li>
      <li>Loading indicators</li>
      <li>Custom prompt input with gradient border (purple to pink)</li>
      <li>Multi-line textarea with minimum height</li>
      <li>Attachment and send buttons positioned in bottom-right corner</li>
      <li>Mulish font for consistent typography</li>
      <li>Fully responsive design</li>
    </ul>
  </div>
</div>