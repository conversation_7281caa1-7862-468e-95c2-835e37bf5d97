<div class="create-agent-container">
  <h1 class="page-title">Choose the type of Agent you want to create</h1>

  <div class="agent-types-container">
    <!-- Individual Agent Card -->
    <ava-text-card
      class="agent-type-card"
      [type]="'prompt'"
      [title]="'Individual Agents'"
      [description]="'Best used for executing just an individual agent'"
      (cardClick)="selectAgentType('individual')"
    >
    </ava-text-card>
    
    <!-- Collaborative Agent Card -->
    <ava-text-card
      class="agent-type-card"
      [type]="'prompt'"
      [title]="'Collaborative Agents'"
      [description]="'Best used for executing many agents in a workflow'"
      (cardClick)="selectAgentType('collaborative')"
    >
    </ava-text-card>
  </div>
</div>
