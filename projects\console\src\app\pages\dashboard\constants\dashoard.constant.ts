export enum APIKeys {
  totalAgentsCreated = 'totalAgentsCreated',
  totalAgentsReused = 'totalAgentsReused',
  totalTools = 'totalTools',
  toolUsage = 'toolUsage',
  agentMetrics = 'agentMetrics',
  toolName = 'toolName',
  agentName = 'agentName',
  usageCount = 'usageCount',
  workflowCount ='workflowCount'
}

export enum ActiveMonitorings {
  tools = 'Tools',
  agents = 'Agents',
}

export const ACTIVE_MONITORING_OPTIONS = Object.values(ActiveMonitorings).map(
  (value) => {
    return {
      name: value,
      value: value,
    };
  },
);

export const DASHBOARD_CARD_DETAILS = [
  {
    icon: '',
    title: 'Active Workflows',
    field: APIKeys.totalAgentsCreated,
    subtitle: 'Agents actively running',
    badge: '',
    value:0
  },
  {
    icon: '',
    title: 'Active Agents',
    field: APIKeys.totalAgentsReused,
    subtitle: 'Agents active',
    badge: '',
    value:0
  },
  {
    icon: '',
    title: 'Agents Approval',
    field: APIKeys.totalTools,
    subtitle: 'Agents actively running',
    badge: '',
    value:0
  },
  {
    icon: '',
    title: 'Failed Agents',
    field: 0.0,
    subtitle: 'Agents actively running',
    badge: '',
    value:0
  },
];
