import { Component, ViewEncapsulation, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { IconComponent } from '@ava/play-comp-library';
import { StudioCard } from '../../interfaces/launchpad.interface';
import { studiosCard } from '../../constants/constant';

@Component({
  selector: 'app-studios',
  templateUrl: './studios.component.html',
  styleUrls: ['./studios.component.scss'],
  standalone: true,
  imports: [CommonModule, RouterModule, IconComponent],
  encapsulation: ViewEncapsulation.None,
})
export class StudiosComponent implements OnInit {
  @Input() selectedStudioIndex: number = 0;
  @Input() showSkeleton: boolean = false;
  @Input() studios: StudioCard[] = studiosCard;
  displayedStudios: StudioCard[] = [];

  constructor(private router: Router) {}

  ngOnInit(): void {
    this.displayedStudios = [this.studios[this.selectedStudioIndex]];
  }

  navigateToStudio(studio: StudioCard): void {
    this.router.navigate([studio.link]);
  }
}
