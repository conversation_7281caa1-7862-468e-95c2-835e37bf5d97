<div class="analytics">

  <!-- Agents Used Today Section -->
  <div class="analytics-section agents-section">
    <div class="section-header">
      <h3>Agents used today</h3>
    </div>
    <div class="section-content">
      <div class="metric-display">
        <div class="metric-value">{{ analyticsData.agentsUsedToday.count }} Agents</div>
        <div class="metric-badge">{{ analyticsData.agentsUsedToday.percentage }}</div>
      </div>
      <div class="chart-container">
        <div class="line-chart">
          <div class="chart-line">
            <svg viewBox="0 0 280 80" class="line-svg">
              <path d="M 10 60 Q 50 40 90 50 Q 130 30 170 45 Q 210 25 250 35 Q 270 30 280 25"
                    stroke="#8B5CF6"
                    stroke-width="2"
                    fill="none"
                    stroke-linecap="round"/>
              <circle cx="280" cy="25" r="3" fill="#8B5CF6"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Time Saved Section -->
  <div class="analytics-section time-section">
    <div class="section-header">
      <h3>Time saved</h3>
    </div>
    <div class="section-content">
      <div class="time-display">
        <div class="time-value">{{ analyticsData.timeSaved.minutes }} Min</div>
        <div class="time-subtitle">{{ analyticsData.timeSaved.agentsUsed }} Agents used</div>
      </div>
      <div class="chart-container">
        <div class="bar-chart">
          <div class="bar-item" style="height: 45%"></div>
          <div class="bar-item" style="height: 30%"></div>
          <div class="bar-item" style="height: 55%"></div>
          <div class="bar-item" style="height: 25%"></div>
          <div class="bar-item" style="height: 40%"></div>
          <div class="bar-item" style="height: 35%"></div>
          <div class="bar-item" style="height: 50%"></div>
        </div>
      </div>
    </div>
  </div>

    <div class="analytics-section task-section">
    <div class="section-header">
      <h3>Task Achieved</h3>
    </div>
    <div class="section-content">
      <div class="task-content">
        <div class="task-percentage">
          <div class="percentage-circle">
            <div class="percentage-text">{{ analyticsData.taskAchieved.percentage }}%</div>
          </div>
        </div>
        <div class="task-legend">
          <div class="legend-item" *ngFor="let category of getTaskAchievedCategories()">
            <div class="legend-dot" [style.background-color]="category.color"></div>
            <span class="legend-label">{{ category.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>