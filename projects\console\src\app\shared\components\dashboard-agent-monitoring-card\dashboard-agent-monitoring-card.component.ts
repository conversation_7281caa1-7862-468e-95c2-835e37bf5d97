import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import {
  IconComponent,
  CardContentComponent,
  TxtCardComponent,
} from '@ava/play-comp-library';

@Component({
  selector: 'app-dashboard-agent-monitoring-card',
  imports: [
    IconComponent,
    CardContentComponent,
    TxtCardComponent,
    CommonModule,
  ],
  templateUrl: './dashboard-agent-monitoring-card.component.html',
  styleUrl: './dashboard-agent-monitoring-card.component.scss',
})
export class DashboardAgentMonitoringCardComponent {
  @Input() activity: any;
}
