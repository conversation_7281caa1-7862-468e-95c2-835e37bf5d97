import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TextCardComponent } from '@ava/play-comp-library';

@Component({
  selector: 'app-create-agent',
  standalone: true,
  imports: [CommonModule, TextCardComponent ],
  templateUrl: './create-agent.component.html',
  styleUrl: './create-agent.component.scss',
})
export class CreateAgentComponent {
  constructor(private router: Router) {}

  selectAgentType(type: 'individual' | 'collaborative'): void {
    console.log(`Selected agent type: ${type}`);
    this.router.navigate(['/build/agents', type]);
  }
}
