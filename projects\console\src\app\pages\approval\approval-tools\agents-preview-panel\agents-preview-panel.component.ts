import { CommonModule, DatePipe } from '@angular/common';
import { Component, Input } from '@angular/core';
import {
  AvaTextboxComponent,
  ButtonComponent,
  IconComponent,
  SliderComponent,
} from '@ava/play-comp-library';
import approvalText from '../../constants/approval.json';
import { PreviewPanelComponent } from '@shared/index';

@Component({
  selector: 'app-agents-preview-panel',
  imports: [
    PreviewPanelComponent,
    IconComponent,
    ButtonComponent,
    DatePipe,
    CommonModule,
    SliderComponent,
    AvaTextboxComponent,
  ],
  templateUrl: './agents-preview-panel.component.html',
  styleUrl: './agents-preview-panel.component.scss',
})
export class AgentsPreviewPanelComponent {
  @Input() previewData: any = null;
  @Input() closePreview!: () => void;
  @Input() editTool!: () => void;
  @Input() rejectApproval!: () => void;
  @Input() approveApproval!: () => void;
  @Input() testApproval!: () => void;
  public labels: any = approvalText.labels;

  showMoreConfig = false;

  toggleConfigDetails(): void {
    this.showMoreConfig = !this.showMoreConfig;
  }

  onButtonClick(event: any): void {
    this.editTool();
  }

  handleTest(): void {
    this.testApproval();
  }

  handleSendback(): void {
    this.closePreview();
    this.rejectApproval();
  }

  handleApprove(): void {
    this.closePreview();
    this.approveApproval();
  }

  getAdditionalFields(data: any): { key: string; value: any }[] {
    const excludeFields = [
      'id',
      'name',
      'description',
      'labelCode',
      'categoryName',
      'categoryId',
    ];
    return Object.keys(data)
      .filter((key) => !excludeFields.includes(key) && data[key] != null)
      .map((key) => ({ key, value: data[key] }));
  }

  getFileIconColor(index: number): string {
    const colors = ['#dc2626', '#2563eb', '#16a34a', '#f59e0b', '#8b5cf6'];
    return colors[index % colors.length];
  }

  getButtonLabel(): string {
    return 'Edit';
  }
}
