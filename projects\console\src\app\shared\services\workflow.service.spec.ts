import { TestBed } from '@angular/core/testing';
import { WorkflowService } from './workflow.service';
import { provideHttpClientTesting, HttpTestingController } from '@angular/common/http/testing';
import { CardData } from '../../shared/models/card.model';

describe('WorkflowService', () => {
  let service: WorkflowService;
  let httpMock: HttpTestingController;

  const mockData: CardData[] = [
    {
      id: '1',
      title: 'Product Recommendation Engine',
      tags: [
        { label: 'E-commerce' },
        { label: 'Recommendation' },
        { label: 'Customer Experience' }
      ],
      createdDate: '2/15/2025',
      actions: [
        { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
        { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
      ],
      userType: 'business',
      client: 'retail',
      department: 'sales',
      role: 'analyst',
      project: 'customer-engagement',
      category: 'recommendation'
    },
    {
      id: '2',
      title: 'Customer Support Ticket Triage',
      tags: [
        { label: 'Support' },
        { label: 'Classification' },
        { label: 'Automation' }
      ],
      createdDate: '1/28/2025',
      actions: [
        { icon: 'execute', action: 'execute', tooltip: 'Execute Workflow' },
        { icon: 'delete', action: 'delete', tooltip: 'Delete Workflow' }
      ],
      userType: 'business',
      client: 'services',
      department: 'customer-support',
      role: 'manager',
      project: 'support-efficiency',
      category: 'automation'
    }
  ];

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        WorkflowService,
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(WorkflowService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should fetch workflows successfully', () => {
    service.fetchAllWorkflows().subscribe(data => {
      expect(data).toEqual(mockData);
    });

    const req = httpMock.expectOne(`${service['apiServiceUrl']}/ava/force/workflow`);
    expect(req.request.method).toBe('GET');
    req.flush(mockData);
  });

  it('should return an empty array on error', () => {
    service.fetchAllWorkflows().subscribe(data => {
      expect(data).toEqual([]);
    });

    const req = httpMock.expectOne(`${service['apiServiceUrl']}/ava/force/workflow`);
    req.flush('Error occurred', { status: 500, statusText: 'Internal Server Error' });
  });
  
  it('should call deleteWorkflowById with correct URL and headers', () => {
    const workflowId = '123';
    const expectedUrl = `${service['apiServiceUrl']}/ava/force/workflow?workflowId=${workflowId}`;

    service.deleteWorkflowById(workflowId).subscribe(response => {
      expect(response).toBeTruthy();
    });

    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe('DELETE');
    req.flush({ success: true });
  });

});
