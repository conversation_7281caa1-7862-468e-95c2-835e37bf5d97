import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DashboardApprovalCardComponent } from './dashboard-approval-card.component';

describe('DashboardApprovalCardComponent', () => {
  let component: DashboardApprovalCardComponent;
  let fixture: ComponentFixture<DashboardApprovalCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DashboardApprovalCardComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DashboardApprovalCardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
