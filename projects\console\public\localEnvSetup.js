// localEnvSetup.js
(function() {
  // Check if running locally
  const isLocal = window.location.hostname === 'localhost';

  if (isLocal) {
    window.env = {
      production: 'false',
      baseUrl: 'https://aava-dev.avateam.io',
      elderWandUrl: 'http://localhost:4200/launchpad/dashboard',
      experienceStudioUrl: 'http://localhost:4201/experience/',
      productStudioUrl: 'http://localhost:4202/product',
      consoleRedirectUrl: 'http://localhost:4203/console/',
      consoleUrl: 'http://localhost:4203/console',
      consoleRedirectUri: 'http://localhost:4203/console',
      accessKey: 'your-access-key',
      apiVersion: 'v1',
      consoleApi: '/v1/api/admin',
      consoleApiV2: '/v2/api/admin',
      consoleApiAuthUrl: '/api/auth',
      consoleEmbeddingApi: '/v1/api/embedding',
      consoleInstructionApi: '/v1/api/instructions',
      consoleLangfuseUrl: 'https://aava-metrics-dev.avateam.io/project/cmddf1yeg000ow807vfnaladj',
      consoleTruelensUrl: 'https://aava-trulens-dev.avateam.io/',
      consolePipelineApi: '/force/platform/pipeline/api/v1',
      experienceApiUrl: '/api/experience',
      productApiUrl: '/api/product',
      enableLogStreaming: 'all',
      logStreamingApiUrl: 'wss://aava-dev.avateam.io/ws-pipeline-log-stream',
      appVersion: '1.0.0',
      workflowExecutionMode: 'all',
      useBasicLogin: 'false'
    };

    // Dynamically construct URLs using the baseUrl
    const baseUrl = window.env.baseUrl;
    Object.keys(window.env).forEach(key => {
      if (key !== 'baseUrl' && key !== 'production' && typeof window.env[key] === 'string' && window.env[key].startsWith('/')) {
        window.env[key] = baseUrl + window.env[key];
      }
    });
  }
})();
