<div class="user__management--wrapper">
  <h2>User Management</h2>

    <div class="user__management--container">
        <div class="filter--section">
            <div class="filter">
              @for (role of filterRoles; track role) {
                <ava-button 
                  [label]="role.label" 
                  [variant]="'primary'" 
                  [variant]="selectedFilterRole() === role.value ? 'primary' : 'secondary'"
                  [pill]="true" 
                  size="small"
                  (userClick)="onRoleFilterChange(role.value)"
                ></ava-button>
              }
            </div>
            <div class="filter--right">
                <ava-textbox
                    [placeholder]="'Search user'"
                    hoverEffect="glow"
                    pressedEffect="solid"
                    size="md"
                    [formControl]="userSearchControl"
                  >
                    <ava-icon
                      slot="icon-start"
                      iconName="search"
                      [iconSize]="16"
                      iconColor="var(--color-brand-primary)"
                    >
                    </ava-icon>
                  </ava-textbox>
                <ava-button 
                  label="Add User" 
                  [customStyles]="{ height: '42px' }" 
                  variant="primary" 
                  size="medium" 
                  (userClick)="addNewUser()"
                >
                </ava-button>
            </div>
        </div>
        <div class="user__management--table">     
            <awe-table-grid 
                [columnDefs]="columns" 
                [serverPagination]="true" 
                [rowData]="rows()" 
                [height]="704" 
                [headerHeight]="64" 
                [pagination]="true"
                [totalItems]="totalRecords()"
                [loading]="loading()" 
                (pageChange)="onPageChange($event)"
            ></awe-table-grid>
        </div>
    </div>
</div>

<!-- Delete Confirmation Popup -->
<ava-popup
  [show]="showUserDeletePopup()"
  title=""
  [message]="'Are you sure you want to delete ?'"
  [showHeaderIcon]="true"
  headerIconName="trash"
  iconColor="#dc3545"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="'Delete'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="onConfirmUserDelete()"
  (cancel)="closeUserDeletePopup()"
  (closed)="closeUserDeletePopup()"
>
</ava-popup>

<ava-popup
  [show]="showDeleteStatusPopup()"
  [title]="'Success'"
  [message]="'user deleted successfully'"
  [showHeaderIcon]="true"
  headerIconName="check-circle"
  iconColor="#28a745"
  [showClose]="true"
  [showCancel]="false"
  (closed)="closeSuccessPopup()"
>
</ava-popup>
