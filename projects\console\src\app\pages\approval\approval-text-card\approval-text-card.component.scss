::ng-deep #dashboard-txt-card{
    .dashboard-txt-card-1 {
    .ava-text-card-wrapper {
        .ava-default-card-container {
            .ava-default-card.default-card.card {
                border: none;
                box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
                height: 300px;
                padding: 24px;
                width: 28rem;
                background: linear-gradient(to left, #ffffff, #f0f5ff);

                ava-card-content {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    height: 100%;

                    .title-wrapper {
                        display: flex;
                        align-items: center;
                        column-gap: 20px;

                        div {
                            margin: 0px;
                            font-weight: 900;
                            font-style: bold;
                            font-size: 20px;
                            color: #3B3F46;
                        }

                    }

                    .value-wrapper {
                        display: flex;
                        flex-direction: column;
                        row-gap: 10px;

                        h1 {
                            margin: 0px;
                            font-weight: 500;
                            font-style: Medium;
                            font-size: 48px;
                            color: #3B3F46;
                        }

                        p {
                            margin: 0px;
                            font-weight: 400;
                            font-style: Regular;
                            font-size: 16px;
                            color: #3B3F46;
                        }
                    }

                    /* Skeleton styles */
                    .skeleton-line,
                    .skeleton-icon {
                        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                        background-size: 200% 100%;
                        animation: shimmer 1.5s infinite linear;
                        border-radius: 6px;
                    }

                    .skeleton-title { width: 60%; height: 20px; }
                    .skeleton-value { width: 40%; height: 44px; }
                    .skeleton-subtitle { width: 80%; height: 16px; }
                    .skeleton-icon { width: 24px; height: 24px; border-radius: 50%; }

                    @keyframes shimmer {
                        0% { background-position: -200% 0; }
                        100% { background-position: 200% 0; }
                    }
                }

                @media (min-width: 1900px) {
                    width: 32rem;
                }

                @media (min-width: 1800px) and (max-width: 1900px) {
                    width: 28rem;
                }

                @media (min-width: 1600px) and (max-width: 1800px) {
                    width: 26rem;
                }

                @media (min-width: 1440px) and (max-width: 1600px) {
                    width: 24rem;
                }

                @media (min-width: 1200px) and (max-width: 1440px) {
                    width: 22rem;
                }
                  
                @media (max-width: 1200px) {
                    width: 20rem;
                }
            }
        }
    }
}
}