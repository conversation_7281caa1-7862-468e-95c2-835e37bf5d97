<div class="p-2 m-4 mt-5 filter-tabs" id="filter-tab-container">
  <div class="tabs-wrapper">
    <!-- Visible Tabs Container -->
    <div class="d-flex g-2 tabs-container">
      <button
        *ngFor="let tab of visibleTabs"
        class="d-flex align-items-center justify-content-center g-2 p-2 tab-item tab-button"
        [class.active]="isActiveTab(tab.id)"
        [class.disabled]="tab.disabled"
        [disabled]="tab.disabled"
        (click)="onTabClick(tab.id, $event)"
      >
        <ng-container *ngIf="tab?.subLabel?.length else noSubLabel">
          <ava-icon
            *ngIf="!getTabDisplaySvgImage(tab)"
            [iconName]="getTabDisplayIcon(tab).iconName"
            iconSize="24px"
            [iconColor]="getTabDisplayIcon(tab).iconColor || '#000'"
          ></ava-icon>

          <span *ngIf="getTabDisplaySvgImage(tab)">        
            <img [src]="getTabDisplaySvgImage(tab)" alt="icon" />
          </span>
        </ng-container>

        <ng-template #noSubLabel>
          <ava-icon
            *ngIf="tab.icon"
            [iconName]="tab.icon"
            iconSize="24px"
            [iconColor]="getTabDisplayIcon(tab).iconColor || '#000'"
          ></ava-icon>

          <span *ngIf="tab.svgImage">        
              <img [src]="tab.svgImage" alt="icon" />
          </span>
        </ng-template>

        <span>{{ getTabDisplayLabel(tab) }}</span>
        <!-- Dropdown arrow for tabs with sub-labels -->
        <ava-icon 
          *ngIf="tab.subLabel && tab.subLabel.length > 0"
          [iconName]="isTabDropdownOpen(tab.id) ? 'chevron-up' : 'chevron-down'"
          iconSize="16px"
          iconColor="#6B7280"
          class="dropdown-arrow"
        ></ava-icon>
      </button>
    </div>
  </div>
</div>

<!-- Dropdown Portal Container -->
<div
  *ngIf="dropdownPortal.open && dropdownPortal.rect"
  class="dropdown-portal-menu"
  [style.left]="dropdownPortal.rect.left + 'px'"
>
  <div class="dropdown-menu">
    <div
      *ngFor="let item of dropdownPortal.items"
      class="dropdown-item"
      (click)="onDropdownItemSelected(item)"
    >
      <ava-icon
        *ngIf="item.icon"
        [iconName]="item.icon"
        iconSize="24px"
        [iconColor]="item.iconColor || '#000'"
      ></ava-icon>
  
      <span *ngIf="!item.icon">        
        <img [src]="item.svgImage" alt="icon" />
      </span>
      <span>{{ item.label }}</span>
    </div>
  </div>
</div>
