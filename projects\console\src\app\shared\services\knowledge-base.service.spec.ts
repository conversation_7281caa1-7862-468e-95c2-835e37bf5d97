import { TestBed } from '@angular/core/testing';
import { provideHttpClientTesting, HttpTestingController } from '@angular/common/http/testing';
import { KnowledgeBaseService } from './knowledge-base.service';
import { environment } from '../../../environments/environment';
import { CardData } from '../../shared/models/card.model';

describe('KnowledgeBaseService', () => {
  let service: KnowledgeBaseService;
  let httpMock: HttpTestingController;

  const baseUrl = `${environment.consoleEmbeddingApi}/ava/force/knowledge`;

  const mockData: CardData[] = [
    {
      id: '1',
      title: 'Knowledge Item 1',
      createdDate: '2025-06-01',
      tags: [{ label: 'AI' }],
      actions: [
        { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
        { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
      ]
    }
  ];

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        KnowledgeBaseService,
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(KnowledgeBaseService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should fetch knowledge data successfully', () => {
    service.fetchAllKnowledge().subscribe(data => {
      expect(data).toEqual(mockData);
    });

    const req = httpMock.expectOne(baseUrl);
    expect(req.request.method).toBe('GET');
    req.flush(mockData);
  });

  it('should return an empty array on error', () => {
    service.fetchAllKnowledge().subscribe(data => {
      expect(data).toEqual([]);
    });

    const req = httpMock.expectOne(baseUrl);
    req.flush('Error', { status: 500, statusText: 'Internal Server Error' });
  });

  it('should include correct actions for each knowledge item', () => {
    service.fetchAllKnowledge().subscribe(data => {
      const item = data[0];
      expect(item.actions?.length).toBe(2);
      expect(item.actions).toContain(jasmine.objectContaining({
        icon: 'execute',
        action: 'execute',
        tooltip: 'Access Knowledge Base'
      }));
      expect(item.actions).toContain(jasmine.objectContaining({
        icon: 'delete',
        action: 'delete',
        tooltip: 'Delete Knowledge Base'
      }));
    });

    const req = httpMock.expectOne(baseUrl);
    req.flush(mockData);
  });

  it('should call DELETE with correct URL and headers', () => {
    const collectionName = 'test-collection';
    const expectedUrl = `${baseUrl}?collectionName=${collectionName}`;
    const mockResponse = { success: true };

    service.deleteByCollection(collectionName).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(expectedUrl);
    expect(req.request.method).toBe('DELETE');
    expect(req.request.headers.get('Content-Type')).toBe('application/json');
    req.flush(mockResponse);
  });

  it('should handle DELETE errors gracefully', () => {
    const collectionName = 'non-existent';
    const expectedUrl = `${baseUrl}?collectionName=${collectionName}`;

    service.deleteByCollection(collectionName).subscribe({
      next: () => fail('Should have thrown error'),
      error: (err) => {
        expect(err.status).toBe(404);
        expect(err.statusText).toBe('Not Found');
      }
    });

    const req = httpMock.expectOne(expectedUrl);
    req.flush('Not Found', { status: 404, statusText: 'Not Found' });
  });
});
