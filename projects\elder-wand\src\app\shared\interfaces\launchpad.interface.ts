export interface HeroContent {
  greeting: string;
  message: string;
}

export interface StudioCard {
  id: number;
  title: string;
  description: string;
  image: string;
  link: string;
  isExternal?: boolean;
}

export interface FilterTab {
  id: string;
  label: string;
  subLabel?: FilterTabSubItem[];
  icon?: string;
  svgImage?: string;
  svgImageColor?: string;
  iconColor?: string;
  priority?: number;
  disabled?: boolean;
  showDropdown?: boolean;
  selectedSubLabel?: string;
}
export interface FilterTabSubItem {
  label: string;
  value?: string;
  icon?: string;
  svgImage?: string;
  iconColor?: string;
  svgImageColor?: string;
}