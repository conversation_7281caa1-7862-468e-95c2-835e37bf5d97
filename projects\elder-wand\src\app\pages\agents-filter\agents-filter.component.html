<div class="d-flex flex-column" id="agent-filter-container">
  <app-hero
    heroType="Marketplace"
    [placeholderText]="placeholderText"
    (searchQueryChange)="onSearchQueryChange($event)"
    (sendClicked)="onSendClicked($event)"
  >
  </app-hero>

  <app-filter-tabs
    [tabs]="filterTabs"
    [activeTab]="activeFilter"
    (tabChange)="onFilterChange($event)"
  >
  </app-filter-tabs>

  <div class="row g-3 agents-grid">
    <!-- Loading skeleton cards -->
    <div
      [ngClass]="
        showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'
      "
      *ngFor="let skeleton of getSkeletonCards()"
      [hidden]="!isLoading"
    >
      <app-agent-card
        [isLoading]="true"
        [variant]="isMarketplace ? 'marketplace' : 'dashboard'"
      ></app-agent-card>
    </div>

    <!-- Actual agent cards -->
    <div
      [ngClass]="
        showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'
      "
      *ngFor="let agent of agentsData"
      [hidden]="isLoading"
    >
      <app-agent-card
        [agent]="agent"
        [isLoading]="false"
        [variant]="isMarketplace ? 'marketplace' : 'dashboard'"
        (cardClick)="onAgentClick($event)"
        (actionClick)="onAgentAction($event)"
      ></app-agent-card>
    </div>
  </div>
</div>
