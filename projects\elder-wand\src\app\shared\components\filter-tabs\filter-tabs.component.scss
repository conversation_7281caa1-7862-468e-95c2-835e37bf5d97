@use "../../../../assets/themes/mixins" as mixins;

:host {
  --dropdown-hover: #f3f4f6;
  --dropdown-bg: #ffffff;
  --dropdown-border: #e0e0e0;
}

#filter-tab-container {
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-border-subtle);
  background: rgba(255, 255, 255, 0.4);
  .tab-item {
    background: transparent;
    border-radius: 8px;
    border: none;
    font-size: 18px;
    font-weight: 400;
    line-height: normal;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &.disabled {
      cursor: not-allowed;
      pointer-events: auto;
      color: var(--black);

      svg {
        stroke: var(--black);
      }

      &:hover {
        background: transparent;
        transform: none;
      }
    }

    i {
      font-size: 16px;
    }

    &:hover {
      background-color: var(--white);
    }

    &.active {
      background: var(--white);
      color: var(--black);
      ava-icon {
        @include mixins.gradient-text(none, initial, initial);
      }

      i {
        @include mixins.gradient-text(inherit, text, transparent);
      }
    }
  }
  .tabs-container {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .tab-item {
      flex: 1;
      min-width: fit-content;
    }
  }
}

.tab-button {
  gap: 4px;
}

.tab-item .dropdown-arrow {
  margin-left: 4px;
  transition: transform 0.2s ease;
}

.dropdown-portal-menu {
  position: fixed;
  z-index: 2000;
  margin-top: -1.5rem;
}

.dropdown-menu {
  background: var(--dropdown-bg) !important;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--dropdown-border) !important;
  min-width: 180px;
  padding: 8px;
  backdrop-filter: blur(20px);
  margin-top: 8px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: var(--dropdown-hover) !important;
    transform: translateX(2px);
  }
}

.dropdown-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  flex-shrink: 0;
}

.dropdown-content {
  flex: 1;
}

.dropdown-label {
  font-weight: 600;
  font-size: 16px;
  color: var(--dropdown-text) !important;
  margin-bottom: 4px;
}