import {
  Component,
  Input,
  Output,
  EventEmitter,
  HostListener,
  OnInit,
  AfterViewInit,
  OnChanges,
  ChangeDetectorRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '@ava/play-comp-library';
import { FilterTab } from '../../interfaces/launchpad.interface';
import { HttpClient } from '@angular/common/http';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-filter-tabs',
  templateUrl: './filter-tabs.component.html',
  styleUrls: ['./filter-tabs.component.scss'],
  standalone: true,
  imports: [CommonModule, IconComponent],
})
export class FilterTabsComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() tabs: FilterTab[] = [];
  @Input() activeTab: string = 'all';
  @Output() tabChange = new EventEmitter<{
    tabValue: string;
    subLabelValue: { [key: string]: string };
  }>();
  sortedTabs: FilterTab[] = [];
  visibleTabs: FilterTab[] = [];
  dropdownTabs: FilterTab[] = [];
  showDropdown = false;

  activeDropdowns: Set<string> = new Set();
  selectedSubDropdownValues: Map<string, any> = new Map();
  // Track sub-label selections for emission
  subLabelSelections: { [key: string]: string } = {};
  // Dropdown portal state
  dropdownPortal: {
    open: boolean;
    rect: DOMRect | null;
    items: any[];
    tabId: string; 
  } = {
    open: false,
    rect: null,
    items: [],
    tabId: ''
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly http: HttpClient,
    private readonly sanitizer: DomSanitizer
  ) {}

  ngOnInit(): void {
    this.initializeSelectedSubLabels();
    this.updateSortedTabs();
    this.updateTabsVisibility();
  }

  ngOnChanges(): void {
    this.initializeSelectedSubLabels();
    this.updateSortedTabs();
    this.updateTabsVisibility();
  }

  private initializeSelectedSubLabels(): void {
    this.tabs.forEach(tab => {
      if (tab.selectedSubLabel && tab?.subLabel?.length) {
        // Find the matching sub-label item
        const selectedItem = tab.subLabel.find(subItem => subItem.label === tab.selectedSubLabel);
        if (selectedItem) {
          this.selectedSubDropdownValues.set(tab.id, selectedItem);
          this.subLabelSelections[tab.id] = selectedItem.label;
        }
      }
    });
  }

  private updateSortedTabs(): void {
    this.sortedTabs = this.tabs
      .map((tab, index) => ({
        ...tab,
        priority: tab.priority ?? this.tabs.length - index,
      }))
      .sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateTabsVisibility();
    });
  }

  @HostListener('window:resize')
  onResize() {
    this.updateTabsVisibility();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const path = event.composedPath?.() || [];
    const clickedInsideDropdown = path.some((el: any) =>
      el?.classList?.contains?.('dropdown-portal-menu'),
    );

    const clickedInsideNavItem = path.some((el: any) =>
      el?.classList?.contains?.('tabs-container'),
    );

    if (
      this.dropdownPortal.open &&
      !clickedInsideDropdown &&
      !clickedInsideNavItem
    ) {
      this.closeDropdownPortal();
      this.activeDropdowns.clear();
    }
  }

  updateTabsVisibility() {
    const container = document.querySelector('.tabs-container');
    if (!container) return;
    const containerWidth = container.clientWidth;
    const filterButtonWidth = 100;
    const availableWidth = containerWidth - filterButtonWidth;
    this.visibleTabs = [];
    this.dropdownTabs = [];
    let currentWidth = 0;
    const averageTabWidth = availableWidth / this.sortedTabs.length;
    for (const tab of this.sortedTabs) {
      const estimatedWidth = this.calculateTabWidth(tab);
      if (
        currentWidth + estimatedWidth <= availableWidth &&
        estimatedWidth <= averageTabWidth * 1.5
      ) {
        this.visibleTabs.push(tab);
        currentWidth += estimatedWidth;
      } else {
        this.dropdownTabs.push(tab);
      }
    }
  }

  private calculateTabWidth(tab: FilterTab): number {
    const textWidth = tab.label.length * 8;
    const padding = 32;
    const iconWidth = tab.icon ? 24 : 0;
    const gap = 8;
    return textWidth + padding + iconWidth + gap;
  }

  toggleDropdown(event: Event) {
    event.stopPropagation();
    this.showDropdown = !this.showDropdown;
  }

  onTabClick(tabId: string, event: MouseEvent): void {
    const tab = this.sortedTabs.find((t) => t.id === tabId);
    if (tab?.disabled) {
      return;
    }

    if(tab?.subLabel?.length) {
      this.openSubDropDown(event, tab?.subLabel, tabId);
      this.toggleTabDropdown(tabId);
      return;
    }

    if (this.activeTab !== tabId) {
      this.activeTab = tabId;
      this.emitTabChange(tabId);
    }
    this.showDropdown = false;
  }

  isActiveTab(tabId: string): boolean {
    return this.activeTab === tabId;
  }

  toggleTabDropdown(tabId: string) {
    if (this.activeDropdowns.has(tabId)) {
      this.activeDropdowns.delete(tabId);
      this.closeDropdownPortal();
    } else {
      this.activeDropdowns.clear();
      this.activeDropdowns.add(tabId);
    }
  }

  openSubDropDown(event: any, items: any[], tabId: string) {
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    this.dropdownPortal = {
      open: true,
      rect: rect,
      items: items,
      tabId: tabId,
    };
    //Force change detection to flush portal render and avoid "frozen" dropdowns
    // this.cdr.detectChanges();
  }

  closeDropdownPortal(): void {
    this.dropdownPortal.open = false;
  }

  onDropdownItemSelected(selectedItem: any) {
    if (this.dropdownPortal.tabId) { 
      this.selectedSubDropdownValues.set(this.dropdownPortal.tabId, selectedItem);
      this.subLabelSelections[this.dropdownPortal.tabId] = selectedItem.label;
    
      // Update the original tab configuration to persist the selection
      const tab = this.tabs.find(t => t.id === this.dropdownPortal.tabId);
      if (tab) {
        tab.selectedSubLabel = selectedItem.label;
      }
      // Emit the change with updated sub-label selection
      this.emitTabChange(this.activeTab);
    }
    this.closeDropdownPortal();
    this.activeDropdowns.clear();
  }

  isTabDropdownOpen(tabId: string): boolean {
    return this.activeDropdowns.has(tabId);
  }

   // Get the display label for a tab (original label or selected subdropdown value)
  getTabDisplayLabel(tab: FilterTab): string {
    const selectedValue = this.selectedSubDropdownValues.get(tab.id);
    return selectedValue ? selectedValue.label : tab.label;
  }

  getTabDisplayIcon(tab: FilterTab) {
    const selectedValue = this.selectedSubDropdownValues.get(tab.id);
    return {
      iconName: selectedValue?.icon ?? tab.icon ?? '',
      iconColor: selectedValue?.iconColor ?? tab?.iconColor ?? ''
    };
  }

  getTabDisplaySvgImage(tab: FilterTab) {
    const selectedValue = this.selectedSubDropdownValues.get(tab.id);
    return selectedValue?.svgImage ?? '';
  }

  private emitTabChange(tabId: string): void {
    const emitData = {
      tabValue: tabId,
      subLabelValue: { ...this.subLabelSelections }
    };
    this.tabChange.emit(emitData);
  }
}
