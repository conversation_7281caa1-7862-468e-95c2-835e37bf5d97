#dasboard-container {
  margin-top: 12px;
}

#dasborad-bottom-container {
  margin-top: 2rem;
}

.fw-600 {
  font-weight: 600;
}

.activity-monitoring-status-label {
  text-align: end;
}

.font-12 {
  font-size: 12px;
}

.active-monitoring {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.active-monitoring-text {
  font-size: 24px;
  font-weight: 700;
  color: #3B3F46;

}

::ng-deep .ava-dropdown {
  width: 100% !important;
}

#dasborad-bottom-container {
  height: 30rem;
}

#activity-monitoring-container {
  padding: 0px 0px 0px 10px;
}

#high-prioirty-container {
  .high-priority-wrapper {
    padding: 12px;
  }
}

.box-wrapper {
  border-radius: 24px;
  background: #fff;

}

.active-monitoring-list {
  height: 440px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 24px;
}

.active-monitoring-wrapper {

  border-radius: 24px;
  padding: 24px;
  background: #fff;

  ava-dropdown {
    position: relative;
    top: -3px;
  }
}

.create-type {
  .ava-card-container .ava-card {
    &.card {
      &::before {
        left: 16px;
      }

      &::after {
        right: -16px;
      }
    }
  }
}

.approval-card-wrapper {
  margin: 10px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.agent-approvals-cards-header {
  height: 36px;
  font-family: Mulish;
  font-weight: 700;
  font-style: Bold;
  font-size: 24px;
  color: #3B3F46;
}

.agent-approvals-cards-footer {
  height: 20px;
  margin-top: 24px;
}

.txt-card-wrapper {
  margin-top: 24px;
  margin-right: 5px;
}


::ng-deep {
  .ava-dropdown {
    button {
      border-radius: 200px !important;
      border-color: #BDBDBD !important;
    }

    .dropdown-toggle {
      width: auto !important;
      gap: 5px;
      padding: 0px 16px !important;
      margin-bottom: 0px !important;

      span {
        max-width: fit-content !important;
        color: #3B3F46 !important;
        font-family: Mulish !important;
        font-weight: 700 !important;
        font-size: 14px !important;

      }

      svg {
        stroke: #3B3F46;
      }
    }
  }
}
