#my__account--container {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.agents-grid {
  overflow-y: auto;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px;
  padding: 2rem 0 0 0;
  margin-bottom: 1rem;
}

.user-profile-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-details {
  .user-name {
    color: #000;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
  }

  .user-email,
  .user-role {
    color: #616874;
    font-size: 14px;
    font-weight: 400;
    margin: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

// My Agents Section
.my-agents-section {
  display: flex;
  flex-direction: column;
  overflow: hidden; 
  // margin: 2rem 0 0 0;
  .section-title {
    color: #000;
    font-size: 32px;
    font-weight: 700;
    margin: 0.5rem 0;
  }
}

.pagination {
  margin: 0 2rem 1rem 2rem;
  padding: 0;
}

::ng-deep .stat-icon-container svg {
  stroke: #ed6999;
}

// Tooltip styling for truncated text
[data-tooltip] {
  position: relative;
  cursor: help;
}

[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: normal;
  max-width: 250px;
  word-wrap: break-word;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  animation: tooltip-fade-in 0.2s ease-in-out forwards;
}

[data-tooltip]:hover::before {
  content: "";
  position: absolute;
  bottom: calc(100% - 6px);
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  animation: tooltip-fade-in 0.2s ease-in-out forwards;
}

@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.profile-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
  padding: 2px;

  .profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.2);
  }
}