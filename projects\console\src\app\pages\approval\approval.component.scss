.approval {
  display: grid;
  grid-template-areas: "left right";
  grid-template-columns: auto 1fr; /* Sidebar auto-width, main fills the rest */
  grid-template-rows: 1fr;
  width: 100%;
  height: calc(100vh - 83px);
}

.approval-left-screen {
  grid-area: left;
  width: 70px;
  transition: all var(--transition-speed) ease;
  overflow: hidden;
  height: 100%;
}

.approval-left-screen.quick-actions-expanded {
  width: 250px;
  margin-right: 15px;
}

/* Right side (main content) */
.approval-right-screen {
  grid-area: right;
  padding: 1rem;
  padding-top: 0px;
  // overflow-y: auto;
  height: calc(100vh - 90px);
}

/* Container stacking */
.approval-title-filter {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem; /* gap below entire header section */
}

/* Title styling */
.approvals-title {
  font-weight: bold;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

/* Filter section layout */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;            /* left and right padding */
  margin-bottom: 1rem;        /* space below filter section */
}

/* Search bars (left) */
.search-bars {
  display: flex;
  gap: 1rem;                  /* space between dropdowns and button */
}

/* Textbox section (right) */
.textbox.section {
  margin-left: 1rem;
}

.textbox.section > div {
  display: flex;
  align-items: center; /* vertically centers them */
  gap: 8px;            /* optional spacing between them */
}

.approval-card-header {
  font-size: 1.25rem;              /* Slightly larger text */
  font-weight: 600;                /* Semi-bold */
  color: #333;                     /* Dark gray text */
  padding: 0.75rem 1rem;           /* Space inside the header */
  margin-bottom: 1rem;             /* Space below the header */
  display: flex;
  align-items: center;
  justify-content: space-between;  /* In case you later add actions on the right */
}


.approval-title-filter {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: nowrap;
  width: 100%;
  padding: 1rem 0;
  padding-top: 0px;
}

.approval-title-filter > ava-text-card {
  flex: 1 1 22%;
  min-width: 200px;
}

.quick-actions-wrapper {
  grid-area: quick-actions;
  // background-color: var(--dashboard-card-bg);
  border-radius: var(--border-radius-standard);
  display: flex;
  flex-direction: column;
  width: 55px;
  height: 100%;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  border: var(--border-thin);
  position: relative;
  
  &:hover {
    box-shadow: var(--shadow-hover);
  }
  
  /* Expanded state - width is controlled by parent grid */
  &.expanded {
    width: 100%; /* Use 100% width to fit the grid cell */
  }

  @media (min-width: 1900px) and (max-width: 1930px) and (max-height: 1100px) {
    height: 100%;
  }
  
  /* Responsive adjustments */
  @media (min-width: 1200px) and (max-width: 1400px) {
    height: 100% !important;
    max-height: 100% !important;
  }
  
  @media (max-width: 1200px) {
    height: 100%;
  }
  
  @media (max-width: 992px) {
    flex-direction: row;
    width: 100%;
    height: 48px;
    
    &.expanded {
      height: auto;
      max-height: 100%;
      width: 100%;
    }
  }
  
  @media (max-width: 576px) {
    height: 100%;
  }
  
  /* Special case for 13" laptops */
  @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
    height: 100%;
    
    ::ng-deep .card-container {
      height: 100%;
    }
  }
  
  /* Card styling */
  ::ng-deep .quick-actions-card {
    height: 100% !important;
    width: 100% !important;
    
    .card-container {
      height: 100% !important;
      width: 100% !important;
      padding: 0 !important;
      overflow: hidden !important;
      display: flex !important;
      flex-direction: column !important;
      
      .card-body {
        padding: 0 !important;
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
      }
    }
    
    .card-content {
      height: 100% !important;
      display: flex !important;
      flex-direction: column !important;
      padding: 0 !important;
      
      @media (max-width: 992px) {
        flex-direction: row !important;
      }
    }
    
    /* Responsive card adjustments */
    @media (max-width: 992px) {
      .card-container {
        height: 48px !important;
        width: 100% !important;
        flex-direction: row !important;
        
        &.expanded {
          height: auto !important;
        }
      }
    }
    
    /* Special case for 13" laptops */
    @media (min-width: 1200px) and (max-width: 1366px) and (max-height: 800px) {
      .card-container {
        width: 100% !important;
      }
    }
  }
}

.quick-actions-content {
  padding: 20px 16px;
  // overflow-y: auto;
  height: 95%;
  flex-grow: 1;
  margin-bottom: 15px;
  background: linear-gradient(to bottom, #ffffff, #f0f5ff);
  
  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 16px; /* Increased gap for better spacing */
    
    .action-button {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 16px; /* Wider gap for better spacing */
      padding: 16px 20px; /* More padding for better touch area */
      border-radius: 12px; /* Rounded corners */
      border: 1px solid #e5e7eb;
      background: #fff;
      background-origin: border-box;
      background-clip: padding-box, border-box;
      --button-effect-color: 33, 90, 214;
      cursor: pointer;
      transition: all var(--transition-speed) ease;
      width: 100%;
      text-align: left;
      color: #fff;

      .action-icon,
      .action-label {
        color: #000000;
      }

      &:hover {
        opacity: 0.9;
        background-color: #f0f5ff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--dashboard-shadow-hover);
        .action-icon,.action-label {
          color: #1d4ed8;
        }
        
        .action-icon img {
          filter: brightness(0) saturate(100%) invert(21%) sepia(94%) saturate(1627%) hue-rotate(215deg) brightness(91%) contrast(101%);
        }
      }
      
      .action-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        
        img {
          width: 20px;
          height: 20px;
          color: black;
          filter: none; /* Make SVG white */  
        }
      }
      
      .action-label {
        font-size: 16px;
        font-weight: 500;
        color: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%); /* Use the new variable */
      }
    }
  }
}

.quick-actions-toggle {
  display: flex;
  align-items: center;
  gap: 100px;
  padding: 20px 16px;
  padding-bottom: 0px;
  cursor: pointer;
  transition: background-color 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  height: 48px;
  padding: 0 16px;
  background: linear-gradient(to right, #ffffff, #f0f5ff);

  .left-title {
    font-size: 20px;
    font-weight: 1000;
    color: #000000;
    margin-bottom: 5px;
  }
  
  
  /* Adjust the toggle for the collapsed state to center the button */
  .quick-actions-wrapper:not(.expanded) & {
    padding: 20px 0;
    justify-content: center;
  }
  
  .toggle-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background-color: transparent;
    position: relative;
    
    /* Add gradient border for expanded state */
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 8px;
      padding: 1px;
      background: var(--dashboard-gradient);
      -webkit-mask: 
        linear-gradient(#fff 0 0) content-box, 
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
      transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    svg {
      transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
      width: 16px;
      height: 16px;
      stroke: var(--dashboard-toggle-stroke);
      z-index: 1;
      
      &.rotate {
        transform: rotate(180deg);
      }
    }
  }
  
  /* Special styling for collapsed state to match other buttons */
  .quick-actions-wrapper:not(.expanded) .toggle-button {
    background: var(--dashboard-gradient);
    transition: background 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    
    &::before {
      display: none;
    }
    
    svg {
      stroke: var(--dashboard-toggle-stroke-collapsed);
      transition: stroke 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }
  
  span {
    font-weight: 580;
    font-size: 16px;
    color: var(--dashboard-text-primary);
    opacity: 1;
    transition: opacity 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.quick-actions-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px; /* Increased gap between items */
  padding: 20px 0; /* More vertical padding */
  height: 95%;
  margin-bottom: 15px;
  background: linear-gradient(to bottom, #ffffff, #f0f5ff);
  
  @media (max-width: 992px) {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    padding: 8px;
  }
  
  .icon-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px; /* Larger button size */
    height: 36px; /* Larger button size */
    border-radius: 8px; /* Slightly rounded corners */
    border: none;
    background: #fff;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    
    &:hover {
      background-color: #f0f5ff;
      opacity: 0.9;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px var(--dashboard-shadow-hover);
      
      .icon-wrapper img {
        filter: brightness(0) saturate(100%) invert(21%) sepia(94%) saturate(1627%) hue-rotate(215deg) brightness(91%) contrast(101%);
      }
    }
    
    .icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      
      img {
        width: 20px;
        height: 20px;
      }
    }
  }
}

.icon-button.active-action {
  background: linear-gradient(to right, #ffffff, #f0f5ff);
  
  .icon-wrapper img {
    filter: brightness(0) saturate(100%) invert(21%) sepia(94%) saturate(1627%) hue-rotate(215deg) brightness(91%) contrast(101%);
  }
}

.action-button.active-action {
  border: 1px solid #f0f5ff !important;
  background-color: #f0f5ff !important;
  
  .action-icon, .action-label {
    color: #1d4ed8 !important;
  }
  
  .action-icon img {
    filter: brightness(0) saturate(100%) invert(21%) sepia(94%) saturate(1627%) hue-rotate(215deg) brightness(91%) contrast(101%) !important;
  }
}

.approval-card-section{
  border-radius: 24px;
  border: 1px solid #DCDCDC;
  background: #F8F8F8;
  height: 780px;
  overflow-y: auto;
}

.approval-card-wrapper {
  margin: 10px;
}

.no-pending-message {
  display: flex;
  // align-items: center;
  justify-content: center;
  margin-top: 20%;
  font-size: 1.2rem;
  color: #000000;
  font-weight: 500;
  text-align: center;
  background: #f8f8f8;
  border-radius: 16px;
  min-height: 100px;
}

/* Responsive (optional) for mobile screens */
@media (max-width: 768px) {
  .filter-section {
    flex-direction: column;
    align-items: stretch;
  }

  .search-bars,
  .textbox.section {
    width: 100%;
    margin: 0 0 0.5rem 0;
    justify-content: center;
  }

  .search-bars {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

