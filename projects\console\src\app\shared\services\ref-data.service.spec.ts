import { TestBed } from '@angular/core/testing';
import { RefDataService } from './ref-data.service';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { environment } from '../../../environments/environment';

describe('RefDataService', () => {
  let service: RefDataService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [RefDataService]
    });
    service = TestBed.inject(RefDataService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get ref Data list', () => {
    const mockResponse = {
      value: JSON.stringify({
        'refData 1': 'refData 1',
        'refData 2': 'refData 2'
      })
    };
    const mockResponses = [
      { value: 'refData 1', label: 'refData 1' },
      { value: 'refData 2', label: 'refData 2' }
    ];
    service.getRefdataList('Role').subscribe(response => {
      expect(response).toEqual(mockResponses);
    });
    const req = httpMock.expectOne(`${environment.consoleApiUrl}/ava/force/refdata?ref_key=Role`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });
});
