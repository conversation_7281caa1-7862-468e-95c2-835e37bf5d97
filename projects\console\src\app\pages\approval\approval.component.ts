import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { CommonModule, formatDate } from '@angular/common';
import {
  ActivatedRoute,
  Router,
  RouterModule,
} from '@angular/router';
import {
  PopupComponent,
  ConfirmationPopupComponent,
  DropdownOption,
  DialogService,
  DialogButton
} from '@ava/play-comp-library';
import { ApprovalService } from '../../shared/services/approval.service';
import { SharedApiServiceService } from '../../shared/services/shared-api-service.service';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import {
  startWith,
  debounceTime,
  distinctUntilChanged,
  map,
} from 'rxjs';
import approvalText from './constants/approval.json';
import { LucideAngularModule } from 'lucide-angular';

export type RequestStatus = 'approved' | 'rejected' | 'review';
@Component({
  selector: 'app-approval',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    LucideAngularModule,

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './approval.component.html',
  styleUrl: './approval.component.scss',
})
export class ApprovalComponent implements OnInit {
  // Labels from constants file
  appLabels = approvalText.labels;

  public searchValue: string = '';
  public totalApprovedApprovals: number = 20;
  public totalPendingApprovals: number = 15;
  public totalApprovals: number = 60;
  public isBasicCollapsed: boolean = false;
  public quickActionsExpanded: boolean = true;
  public consoleApproval: any = {};
  public options: DropdownOption[] = [];
  public basicSidebarItems: any[] = [];
  public quickActions: any[] = [];
  public toolReviews: any[] = [];
  public workflowReviews: any[] = [];
  public agentsReviews: any[] = [];
  public currentToolsPage = 1;
  public currentAgentsPage = 1;
  public currentWorkflowsPage = 1;
  public pageSize = 50;
  public totalRecords = 0;
  public isDeleted = false;
  public currentTab = '';
  public showToolApprovalPopup = false;
  public showInfoPopup = false;
  public showErrorPopup = false;
  public infoMessage = '';
  public selectedIndex = 0;
  public showFeedbackPopup = false;
  public searchForm!: FormGroup;
  public labels: any = approvalText.labels;
  public approvedAgentId: number | null = null;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private apiService: SharedApiServiceService,
    private approvalService: ApprovalService,
    private fb: FormBuilder,
    private dialogService: DialogService,
  ) {
    this.labels = approvalText.labels;
    this.options = [
      { name: this.labels.electronics, value: 'electronics' },
      { name: this.labels.clothing, value: 'clothing' },
      { name: this.labels.books, value: 'books' },
    ];
    this.basicSidebarItems = [
      {
        id: '1',
        icon: 'hammer',
        text: this.labels.agents,
        route: '',
        active: true,
      },
      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },
      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },
    ];
    this.quickActions = [
      {
        icon: 'awe_agents',
        label: this.labels.agents,
        route: '',
      },
      {
        icon: 'awe_workflows',
        label: this.labels.workflows,
        route: '',
      },
      {
        icon: 'awe_tools',
        label: this.labels.tools,
        route: '',
      },
    ];
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchList();
    this.totalApprovals = 60;
    // Initialize currentTab to the first option (Agents)
    this.currentTab = this.labels.agents;
    if(this.router.url.includes('approval-agents')){
      this.currentTab = this.labels.agents;
    }else if(this.router.url.includes('approval-tools')){
      this.currentTab = this.labels.tools;
    }else if(this.router.url.includes('approval-workflows')){
      this.currentTab = this.labels.workflows;
    }
  }

  public searchList() {
    console.log(this.searchForm.get('search')?.value);
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
        map((value) => value?.toLowerCase() ?? ''),
      )
      .subscribe((searchText) => {
        this.applyFilter(searchText);
      });
  }

  public applyFilter(text: string) {
    const lower = text;

    if (!this.searchValue) {
      if (this.currentTab === 'Agents') {
        this.updateConsoleApproval(this.agentsReviews, 'agent');
      } else if (this.currentTab === 'Tools') {
        this.updateConsoleApproval(this.toolReviews, 'tool');
      } else {
        this.updateConsoleApproval(this.workflowReviews, 'workflow');
      }
      return;
    }

    if (this.currentTab === 'Agents') {
      const filtered = this.agentsReviews.filter((item) =>
        item.agentName?.toLowerCase().includes(lower),
      );
      this.updateConsoleApproval(filtered, 'agent');
    } else if (this.currentTab === 'Tools') {
      const filtered = this.toolReviews.filter((item) =>
        item.toolName?.toLowerCase().includes(lower),
      );
      this.updateConsoleApproval(filtered, 'tool');
    } else {
      const filtered = this.workflowReviews.filter((item) =>
        item.name?.toLowerCase().includes(lower),
      );
      this.updateConsoleApproval(filtered, 'workflow');
    }
  }

  public onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }

  public uClick(i: any) {
    console.log('log' + i);
  }

  public toggleQuickActions(): void {
    this.quickActionsExpanded = !this.quickActionsExpanded;
  }

  public onBasicCollapseToggle(isCollapsed: boolean): void {
    this.isBasicCollapsed = isCollapsed;
    console.log('Basic sidebar collapsed:', isCollapsed);
  }

  public onBasicItemClick(item: any): void {
    this.basicSidebarItems.forEach((i) => (i.active = false));
    item.active = true;
    console.log(item);
  }

  public toRequestStatus(value: string | null | undefined): RequestStatus {
    return value === 'approved' || value === 'rejected' || value === 'review'
      ? value
      : 'review';
  }

  public loadToolReviews() {
    this.approvalService
      .getAllReviewTools(this.currentToolsPage, this.pageSize, this.isDeleted)
      .subscribe((response) => {
        if (this.currentToolsPage > 1) {
          this.toolReviews = [
            ...this.toolReviews,
            ...response.userToolReviewDetails,
          ];
        } else {
          this.toolReviews = response?.userToolReviewDetails;
        }
        this.toolReviews = this.toolReviews.filter(
          (r) => r.status !== 'approved',
        );
        this.totalRecords = this.toolReviews.length;
        this.updateConsoleApproval(this.toolReviews, 'tool');
      });
  }

  public loadWorkflowReviews() {
    this.approvalService
      .getAllReviewWorkflows(
        this.currentWorkflowsPage,
        this.pageSize,
        this.isDeleted,
      )
      .subscribe((response) => {
        if (this.currentWorkflowsPage > 1) {
          this.workflowReviews = [
            ...this.workflowReviews,
            ...response.workflowReviewDetails,
          ];
        } else {
          this.workflowReviews = response?.workflowReviewDetails;
        }
        this.workflowReviews = this.workflowReviews.filter(
          (r) => r.status !== 'approved',
        );
        this.totalRecords = this.workflowReviews.length;
        console.log('reviews ', this.workflowReviews);
        this.updateConsoleApproval(this.workflowReviews, 'workflow');
      });
  }

  public loadAgentsReviews() {
    this.approvalService
      .getAllReviewAgents(this.currentAgentsPage, this.pageSize, this.isDeleted)
      .subscribe((response) => {
        if (this.currentAgentsPage > 1) {
          this.agentsReviews = [
            ...this.agentsReviews,
            ...response.agentReviewDetails,
          ];
        } else {
          this.agentsReviews = response?.agentReviewDetails;
        }
        this.agentsReviews = this.agentsReviews.filter(
          (r) => r.status !== 'approved',
        );
        this.totalRecords = this.agentsReviews.length;
        this.updateConsoleApproval(this.agentsReviews, 'agent');
      });
  }

  public loadMoreTools(page: number) {
    this.currentToolsPage = page;
    this.loadToolReviews();
  }

  public loadMoreAgents(page: number) {
    this.currentAgentsPage = page;
    this.loadAgentsReviews();
  }

  public loadMoreWorkflows(page: number) {
    this.currentWorkflowsPage = page;
    this.loadWorkflowReviews();
  }

  public loadReviews(name: string) {
    this.currentTab = name;
    if (name == 'Tools') {
      this.loadToolReviews();
    } else if (name == 'Agents') {
      this.loadAgentsReviews();
    } else {
      this.loadWorkflowReviews();
    }
  }

  public redirectToListOfApproval(name: string) {
    console.log('Redirecting to:', name);
    console.log('Current labels:', this.labels);
    this.currentTab = name;
    console.log('Updated currentTab to:', this.currentTab);
    
    if (name === this.labels.tools) {
      this.router.navigate(['approval-tools'], { relativeTo: this.route });
    } else if (name === this.labels.agents) {
      this.router.navigate(['approval-agents'], { relativeTo: this.route });
    } else if (name === this.labels.workflows) {
      this.router.navigate(['approval-workflows'], { relativeTo: this.route });
    }
  }

  public rejectApproval(idx: any) {
    console.log(idx);
    this.selectedIndex = idx;
    this.showFeedbackDialog();
  }

  public approveApproval(idx: any) {
    console.log(idx);
    this.selectedIndex = idx;
    this.showApprovalDialog();
  }

  // Replace popup methods with DialogService methods
  private showApprovalDialog(): void {
    this.dialogService.confirmation({
      title: this.labels.confirmApproval,
      message: `${this.labels.youAreAboutToApproveThis} ${this.currentTab}. ${this.labels.itWillBeActiveAndAvailableIn} ${this.currentTab} ${this.labels.catalogueForUsersToExecute}`,
      confirmButtonText: this.labels.approve,
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'danger',
      icon:'circle-check'
    }).then(result => {
      if (result.confirmed) {
        this.handleApproval();
      }
    });
  }

  private showFeedbackDialog(): void {
     const customButtons: DialogButton[] = [
      { label: 'Cancel', variant: 'secondary', action: 'cancel' },
      { label: 'Send Back', variant: 'primary', action: 'sendback' }
    ];
    this.dialogService.feedback({
      title: 'Confirm Send Back',
      message: `This ${this.currentTab} will be send back for corrections and modification. Kindly comment what needs to be done.`,
      buttons:customButtons
    }).then(result => {
      if (result.confirmed && result.confirmed === true) {
        this.handleRejection(result.data);
      }
    });
  }

  public handleTesting(index: any) {
    console.log(index);
    if (this.currentTab == 'Tools') {
      const toolId = this.toolReviews[index].toolId;
      this.redirectToToolPlayground(toolId);
    } else if (this.currentTab == 'Agents') {
      const agentId = this.agentsReviews[index].agentId;
      this.redirectToAgentsPlayground(agentId, 'collaborative');
    } else {
      const workflowId = this.workflowReviews[index].workflowId;
      this.redirectToWorkflowPlayground(workflowId);
    }
  }

  public redirectToToolPlayground(id: number): void {
    this.router.navigate(['/libraries/tools/execute', id]);
  }

  public redirectToWorkflowPlayground(id: number): void {
    this.router.navigate(['/build/workflows/execute', id]);
  }

  public redirectToAgentsPlayground(id: number, type: string): void {
    this.router.navigate(['/build/agents', type, 'execute'], {
      queryParams: { id: id },
    });
  }

  public handleApproval() {
    if (this.currentTab == 'Tools') {
      this.handleToolApproval();
    } else if (this.currentTab == 'Agents') {
      // this.handleAgentApproval();
    } else {
      this.handleWorkflowApproval();
    }
  }

  public handleRejection(feedback: any) {
    if (this.currentTab == 'Tools') {
      this.handleToolRejection(feedback);
    } else if (this.currentTab == 'Agents') {
      // this.handleAgentRejection(feedback);
    } else {
      this.handleWorkflowRejection(feedback);
    }
  }

  public handleToolApproval() {
    const toolDetails = this.toolReviews[this.selectedIndex];
    const id = toolDetails.id;
    const toolId = toolDetails.toolId;
    const status = 'approved';
    const reviewedBy = toolDetails.reviewedBy;

    // Show loading dialog
    this.dialogService.loading({
      title: 'Approving Tool...',
      message: 'Please wait while we approve the tool.',
      showProgress: false,
      showCancelButton: false
    });

    this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({
      next: (response: any) => {
        this.dialogService.close(); // Close loading dialog
        
        const message = response?.message || this.labels.toolSuccessApproveMessage;
        this.dialogService.success({
          title: 'Tool Approved',
          message: message
        }).then(() => {
          this.loadToolReviews(); // Refresh the list
        });
      },
      error: (error) => {
        this.dialogService.close(); // Close loading dialog
        
        const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
        this.dialogService.error({
          title: 'Approval Failed',
          message: errorMessage,
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.handleToolApproval();
          }
        });
      },
    });
  }

  public handleToolRejection(feedback: any) {
    const toolDetails = this.toolReviews[this.selectedIndex];
    const id = toolDetails.id;
    const toolId = toolDetails.toolId;
    const status = 'rejected';
    const reviewedBy = toolDetails.reviewedBy;
    const message = feedback;

    // Show loading dialog
    this.dialogService.loading({
      title: 'Rejecting Tool...',
      message: 'Please wait while we reject the tool.',
      showProgress: false,
      showCancelButton: false
    });

    this.approvalService
      .rejectTool(id, toolId, status, reviewedBy, message)
      .subscribe({
        next: (response: any) => {
          this.dialogService.close(); // Close loading dialog
          
          const message = response?.message || this.labels.toolSuccessRejectMessage;
          this.dialogService.success({
            title: 'Tool Rejected',
            message: message
          }).then(() => {
            this.loadToolReviews(); // Refresh the list
          });
        },
        error: (error) => {
          this.dialogService.close(); // Close loading dialog
          
          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
          this.dialogService.error({
            title: 'Rejection Failed',
            message: errorMessage,
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.handleToolRejection(feedback);
            }
          });
        },
      });
  }

  public handleWorkflowApproval() {
    const workflowDetails = this.workflowReviews[this.selectedIndex];
    const id = workflowDetails?.id;
    const workflowId = workflowDetails?.workflowId;
    const status = 'approved';
    const reviewedBy = workflowDetails?.reviewedBy;
    console.log(id, workflowId, status, reviewedBy);

    // Show loading dialog
    this.dialogService.loading({
      title: 'Approving Workflow...',
      message: 'Please wait while we approve the workflow.',
      showProgress: false,
      showCancelButton: false
    });

    this.approvalService
      .approveWorkflow(id, workflowId, status, reviewedBy)
      .subscribe({
        next: (response: any) => {
          this.dialogService.close(); // Close loading dialog
          
          const message = response?.message || this.labels.workflowSuccessApproveMessage;
          this.dialogService.success({
            title: 'Workflow Approved',
            message: message
          }).then(() => {
            this.loadWorkflowReviews(); // Refresh the list
          });
        },
        error: (error) => {
          this.dialogService.close(); // Close loading dialog
          
          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
          this.dialogService.error({
            title: 'Approval Failed',
            message: errorMessage,
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.handleWorkflowApproval();
            }
          });
        },
      });
  }

  public handleWorkflowRejection(feedback: any) {
    const workflowDetails = this.workflowReviews[this.selectedIndex];
    const id = workflowDetails?.id;
    const workflowId = workflowDetails?.workflowId;
    const status = 'rejected';
    const reviewedBy = workflowDetails?.reviewedBy;
    const message = feedback;
    console.log(id, workflowId, status, reviewedBy, message);

    // Show loading dialog
    this.dialogService.loading({
      title: 'Rejecting Workflow...',
      message: 'Please wait while we reject the workflow.',
      showProgress: false,
      showCancelButton: false
    });

    this.approvalService
      .rejectWorkflow(id, workflowId, status, reviewedBy, message)
      .subscribe({
        next: (response: any) => {
          this.dialogService.close(); // Close loading dialog
          
          const message = response?.message || this.labels.workflowSuccessRejectMessage;
          this.dialogService.success({
            title: 'Workflow Rejected',
            message: message
          }).then(() => {
            this.loadWorkflowReviews(); // Refresh the list
          });
        },
        error: (error) => {
          this.dialogService.close(); // Close loading dialog
          
          const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
          this.dialogService.error({
            title: 'Rejection Failed',
            message: errorMessage,
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            if (result.action === 'retry') {
              this.handleWorkflowRejection(feedback);
            }
          });
        },
      });
  }

  // Remove old handleInfoPopup method - no longer needed as DialogService handles navigation automatically

  public updateConsoleApproval(data: any[], type: string) {
    this.consoleApproval = {
      contents: data?.map((req: any) => {
        const statusIcons: Record<RequestStatus, string> = {
          approved: 'circle-check-big',
          rejected: 'circle-x',
          review: 'clock',
        };
        const statusTexts: Record<RequestStatus, string> = {
          approved: this.labels.approved,
          rejected: this.labels.rejected,
          review: this.labels.review,
        };
        const statusKey = this.toRequestStatus(req?.status);
        let specificId = 0;
        let title = '';

        if (type === 'tool') {
          specificId = req.toolId;
          title = req.toolName;
        } else if (type === 'agent') {
          specificId = req.agentId;
          title = req.agentName;
        } else {
          specificId = req.workflowId;
          title = req.workflowName;
        }

        return {
          id: req.id,
          refId: specificId,
          type: type,
          session1: {
            title: title,
            labels: [
              {
                name: type,
                color: 'success',
                background: 'red',
                type: 'normal',
              },
              {
                name: req.changeRequestType,
                color: req.changeRequestType === 'update' ? 'error' : 'info',
                background: 'red',
                type: 'pill',
              },
            ],
          },
          session2: [
            {
              name: type,
              color: 'default',
              background: 'red',
              type: 'normal',
            },
            {
              name: req.status,
              color: 'default',
              background: 'red',
              type: 'normal',
          },
          ],
          session3: [
            {
              iconName: 'user',
              label: req.requestedBy,
            },
            {
              iconName: 'calendar-days',
              label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),
            },
          ],
          session4: {
            status: statusTexts[statusKey],
            iconName: statusIcons[statusKey],
          },
        };
      }),
      footer: {},
    };
  }
}
