import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import {
  IconComponent,
  ButtonComponent as AvaButtonComponent,
} from '@ava/play-comp-library';
import { Agent, EntityResult } from '../../interfaces/agent-list.interface';

export interface AgentWithEntityData extends Agent {
  entityData?: EntityResult;
}

@Component({
  selector: 'app-agent-details-panel',
  templateUrl: './agent-details-panel.component.html',
  styleUrls: ['./agent-details-panel.component.scss'],
  imports: [CommonModule, IconComponent, AvaButtonComponent],
})
export class AgentDetailsPanelComponent implements OnInit {
  @Input() agent: any;
  @Input() showSkeleton: boolean = false; // New skeleton input
  @Output() close = new EventEmitter<void>();
  @Output() goToPlayground = new EventEmitter<AgentWithEntityData>();

  isDescriptionExpanded = false;
  isLoading = false;
  error: string | null = null;

  constructor(
    private router: Router,
    private http: HttpClient,
  ) {}

  ngOnInit(): void {
    if (!this.showSkeleton && this.agent?.id) {
      this.fetchAgentDetails();
    }
  }

  /**
   * Fetches agent details from the API endpoint
   */
  fetchAgentDetails(): void {
    if (!this.agent?.id) {
      this.error = 'No agent ID provided';
      return;
    }

    this.isLoading = true;
    this.error = null;

    const apiUrl = `https://aava-dev.avateam.io/agents/individual/details?individualAgentId=${this.agent.id}`;

    this.http.get<EntityResult>(apiUrl).subscribe({
      next: (response: EntityResult) => {
        if (this.agent) {
          // Map the API response to the agent's entityData
          this.agent = {
            entityData: response,
          };
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching agent details:', error);
        this.error = 'Failed to load agent details. Please try again.';
        this.isLoading = false;
      },
    });
  }

  /**
   * Closes the agent details panel
   */
  onClose(): void {
    this.close.emit();
  }

  /**
   * Navigates to the playground for the selected agent
   */
  onGoToPlayground(): void {
    if (this.agent) {
      this.goToPlayground.emit(this.agent);
    }
  }

  /**
   * Toggles the description expanded state
   */
  toggleDescription(): void {
    this.isDescriptionExpanded = !this.isDescriptionExpanded;
  }

  /**
   * Get the agent name from entity data or fallback to title
   */
  getAgentName(): string {
    console.log(this.agent)
    return this.agent?.entityData.useCaseName;
  }

  /**
   * Get the agent description from entity data or fallback to description
   */
  getAgentDescription(): string {
    return (
      this.agent?.entityData?.description
    );
  }

  /**
   * Get the agent details from entity data
   */
  getAgentDetails(): string {
    return this.agent?.entityData?.details || 'No details available';
  }

  /**
   * Get the created by information
   */
  getCreatedBy(): string {
    return this.agent?.entityData?.createdBy || 'Unknown';
  }

  /**
   * Get the modified by information
   */
  getModifiedBy(): string {
    return this.agent?.entityData?.modifiedBy || 'Unknown';
  }

  /**
   * Get the agent score
   */
  getAgentScore(): number {
    return this.agent?.entityData?.score || 0;
  }

  /**
   * Check if we should show the skeleton loader
   */
  get shouldShowSkeleton(): boolean {
    return this.showSkeleton || (this.isLoading && !this.error);
  }

  /**
   * Check if we should show the actual content
   */
  get shouldShowContent(): boolean {
    return !this.showSkeleton && !this.isLoading && !this.error && this.agent;
  }
}
