import { Routes } from '@angular/router';
import { AuthGuard } from '@shared/auth/guards/auth.guard';

export const routes: Routes = [
  {
    path: 'callback',
    loadComponent: () =>
      import('@shared/auth/components/callback/callback.component').then(
        (m) => m.CallbackComponent,
      ),
  },
  {
    path: '',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/launchpad-home/launchpad-home.component').then(
        (m) => m.LaunchpadHomeComponent,
      ),
  },
  {
    path: 'agent-list',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/agents-filter/agents-filter.component').then(
        (m) => m.AgentsFilterComponent,
      ),
  },
  {
    path: 'my-account',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/account/view-account.component').then((r) => r.ViewAccountComponent),
  },
  {
    path: 'analytics',
    canActivate: [AuthGuard],
    loadComponent: () =>
      import('./pages/analytics/analytics.component').then(
        (m) => m.AnalyticsComponent,
      ),
  },
  {
    path: 'build',
    canActivate: [AuthGuard],
    children: [
      {
        path: 'agents',
        loadComponent: () =>
          import('@shared/pages/agents/agents.component').then(
            (m) => m.AgentsComponent,
          ),
      },
      {
        path: 'agents/:type',
        loadComponent: () =>
          import(
            '@shared/pages/agents/build-agents/build-agents.component'
          ).then((m) => m.BuildAgentsComponent),
      },
      {
        path: 'agents/:type/execute',
        loadComponent: () =>
          import(
            '@shared/pages/agents/agent-execution/agent-execution.component'
          ).then((m) => m.AgentExecutionComponent),
      },
      {
        path: 'workflows',
        loadComponent: () =>
          import('@shared/pages/workflows/workflows.component').then(
            (m) => m.WorkflowsComponent,
          ),
      },
      {
        path: 'workflows/create',
        loadComponent: () =>
          import(
            '@shared/pages/workflows/workflow-editor/workflow-editor.component'
          ).then((m) => m.WorkflowEditorComponent),
      },
      {
        path: 'workflows/edit/:id',
        loadComponent: () =>
          import(
            '@shared/pages/workflows/workflow-editor/workflow-editor.component'
          ).then((m) => m.WorkflowEditorComponent),
      },
      {
        path: 'workflows/execute/:id',
        loadComponent: () =>
          import(
            '@shared/pages/workflows/workflow-execution/workflow-execution.component'
          ).then((m) => m.WorkflowExecutionComponent),
      },
    ],
  },
  {
    path: 'libraries',
    canActivate: [AuthGuard],
    children: [
      {
        path: 'prompts',
        loadComponent: () =>
          import('@shared/pages/libraries/prompts/prompts.component').then(
            (m) => m.PromptsComponent,
          ),
      },
      {
        path: 'prompts/create',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/prompts/create-prompts/create-prompts.component'
          ).then((m) => m.CreatePromptsComponent),
      },
      {
        path: 'prompts/edit/:id',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/prompts/create-prompts/create-prompts.component'
          ).then((m) => m.CreatePromptsComponent),
      },
      {
        path: 'models',
        loadComponent: () =>
          import('@shared/pages/libraries/models/models.component').then(
            (m) => m.ModelsComponent,
          ),
      },
      {
        path: 'models/create',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/models/create-models/create-models.component'
          ).then((m) => m.CreateModelsComponent),
      },
      {
        path: 'models/edit/:id',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/models/create-models/create-models.component'
          ).then((m) => m.CreateModelsComponent),
      },
      {
        path: 'knowledge-base',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/knowledge-base/knowledge-base.component'
          ).then((m) => m.KnowledgeBaseComponent),
      },
      {
        path: 'knowledge-base/create',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/knowledge-base/create-knowledge-base/create-knowledge-base.component'
          ).then((m) => m.CreateKnowledgeBaseComponent),
      },
      {
        path: 'knowledge-base/edit/:id',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/knowledge-base/create-knowledge-base/create-knowledge-base.component'
          ).then((m) => m.CreateKnowledgeBaseComponent),
      },
      {
        path: 'tools',
        loadComponent: () =>
          import('@shared/pages/libraries/tools/tools.component').then(
            (m) => m.ToolsComponent,
          ),
      },
      {
        path: 'tools/create',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/tools/create-tools/create-tools.component'
          ).then((m) => m.CreateToolsComponent),
      },
      {
        path: 'tools/edit/:id',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/tools/create-tools/create-tools.component'
          ).then((m) => m.CreateToolsComponent),
      },
      {
        path: 'tools/execute/:id',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/tools/create-tools/create-tools.component'
          ).then((m) => m.CreateToolsComponent),
      },
      {
        path: 'tools/clone/:id',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/tools/create-tools/create-tools.component'
          ).then((m) => m.CreateToolsComponent),
      },
      {
        path: 'guardrails',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/guardrails/guardrails.component'
          ).then((m) => m.GuardrailsComponent),
      },
      {
        path: 'guardrails/create',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/guardrails/create-guardrails/create-guardrails.component'
          ).then((m) => m.CreateGuardrailsComponent),
      },
      {
        path: 'guardrails/edit/:id',
        loadComponent: () =>
          import(
            '@shared/pages/libraries/guardrails/create-guardrails/create-guardrails.component'
          ).then((m) => m.CreateGuardrailsComponent),
      },
    ],
  },
  {
    path: '**',
    redirectTo: '/',
  },
];
