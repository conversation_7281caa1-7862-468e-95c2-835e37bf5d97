import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  ImageCardComponent,
  CardContentComponent,
  AvaTagComponent,
} from '@ava/play-comp-library';
import { TokenStorageService } from '@shared/index';

@Component({
  selector: 'app-dashboard-img-card',
  imports: [ImageCardComponent, CardContentComponent, AvaTagComponent],
  templateUrl: './dashboard-img-card.component.html',
  styleUrl: './dashboard-img-card.component.scss',
})
export class DashboardImgCardComponent implements OnInit {
  @Input() userName = '';
  @Input() desc = 'Let’s build Milestones!';

  constructor(
    private tokenStorage: TokenStorageService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.userName = this.tokenStorage.getDaName() || 'User';
  }

  navigateTo(type: string): void {
    const routes: Record<string, string> = {
      agent: '/build/agents/individual',
      workflow: '/build/workflows/create',
      tool: '/libraries/tools/create',
      prompt: '/libraries/prompts/create',
      guardrail: '/libraries/guardrails/create',
    };

    const route = routes[type];
    if (route) {
      this.router.navigate([route]);
    }
  }
}
