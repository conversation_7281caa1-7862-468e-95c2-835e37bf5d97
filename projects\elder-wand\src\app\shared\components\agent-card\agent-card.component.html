<div
  class="agent-card"
  [class.marketplace-card]="variant === 'marketplace'"
  [class.dashboard-card]="variant === 'dashboard'"
  [class.loading]="isLoading"
  (click)="onCardClick()"
>
  <!-- Loading Skeleton -->
  <div *ngIf="isLoading" class="skeleton-content">
    <div class="skeleton-header">
      <div class="skeleton-title"></div>
      <div class="skeleton-rating" *ngIf="variant === 'marketplace'">
        <div class="skeleton-star"></div>
        <div class="skeleton-rating-text"></div>
      </div>
      <div class="skeleton-status" *ngIf="variant === 'dashboard'"></div>
    </div>

    <div class="skeleton-description">
      <div class="skeleton-line"></div>
      <div class="skeleton-line"></div>
      <div class="skeleton-line short"></div>
    </div>

    <div class="skeleton-footer">
      <div class="skeleton-user" *ngIf="variant === 'marketplace'">
        <div class="skeleton-icon"></div>
        <div class="skeleton-user-text"></div>
      </div>
      <div class="skeleton-meta" *ngIf="variant === 'dashboard'">
        <div class="skeleton-date"></div>
      </div>
      <div class="skeleton-time" *ngIf="variant === 'marketplace'"></div>
      <div class="skeleton-actions" *ngIf="variant === 'dashboard'">
        <div class="skeleton-action-btn"></div>
        <div class="skeleton-action-btn"></div>
        <div class="skeleton-action-btn"></div>
        <div class="skeleton-action-btn primary"></div>
      </div>
    </div>
  </div>

  <!-- Actual Content -->
  <div *ngIf="!isLoading && agent" class="card-content">
    <!-- Marketplace Header -->
    <div
      *ngIf="variant === 'marketplace'"
      class="card-header marketplace-header"
    >
      <h2
        class="agent-title-truncate"
        [title]="agent.title"
        [attr.data-tooltip]="agent.title"
      >
        {{ agent.title }}
      </h2>
      <div class="rating" *ngIf="agent.rating">
        <ava-icon
          iconName="star"
          iconSize="18px"
          iconColor="#FFD700"
          class="agent_star-icon"
        ></ava-icon>
        {{ agent.rating }}
      </div>
    </div>

    <!-- Dashboard Header -->
    <div *ngIf="variant === 'dashboard'" class="card-header dashboard-header">
      <h3
        class="agent-title"
        [title]="agent.title"
        [attr.data-tooltip]="agent.title"
      >
        {{ agent.title }}
      </h3>
      <div
        class="status-badge"
        [ngClass]="getStatusClass(agent.status)"
        *ngIf="agent.status"
      >
        {{ agent.status }}
      </div>
    </div>

    <!-- Description -->
    <p
      class="agent-description"
      [title]="agent.description"
      [attr.data-tooltip]="agent.description"
    >
      {{ truncateText(agent.description, 75) }}
    </p>

    <!-- Marketplace Footer -->
    <div
      *ngIf="variant === 'marketplace'"
      class="card-footer marketplace-footer"
    >
      <div class="users" *ngIf="agent.users">
        <ava-icon
          iconName="user"
          iconSize="16px"
          iconColor="#858aad"
          class="profile-svg-icon"
        ></ava-icon>
        {{ agent.users }}
      </div>
      <div class="agent-time-ago">
        {{ agent.timeAgo || "3 days ago" }}
      </div>
    </div>

    <!-- Dashboard Footer -->
    <div *ngIf="variant === 'dashboard'" class="card-footer dashboard-footer">
      <div class="meta-info">
        <div class="meta-item" *ngIf="agent.createdDate">
          <span class="meta-text">Created: {{ agent.createdDate }}</span>
        </div>
      </div>

      <div class="action-buttons">
        <button
          class="action-btn"
          title="View Details"
          (click)="onActionClick($event, 'view')"
        >
          <ava-icon iconName="eye" iconColor="#4C515B" iconSize="16"></ava-icon>
        </button>
        <button
          class="action-btn"
          title="Edit Agent"
          (click)="onActionClick($event, 'edit')"
        >
          <ava-icon
            iconName="pencil"
            iconColor="#4C515B"
            iconSize="16"
          ></ava-icon>
        </button>
        <button
          class="action-btn"
          title="Delete Agent"
          (click)="onActionClick($event, 'delete')"
        >
          <ava-icon
            iconName="trash"
            iconColor="#4C515B"
            iconSize="16"
          ></ava-icon>
        </button>
        <button
          class="action-btn primary"
          title="Execute Agent"
          (click)="onActionClick($event, 'play')"
        >
          <ava-icon iconName="play" iconColor="#fff" iconSize="14"></ava-icon>
        </button>
      </div>
    </div>
  </div>
</div>
