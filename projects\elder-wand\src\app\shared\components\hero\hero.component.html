<div
  id="hero-container"
  class="d-flex flex-column align-items-center justify-content-center"
  [class.marketplace-hero]="heroType === 'Marketplace'"
>
  <div class="d-flex flex-column mb-4 g-4 greeting-section-container">
    <div class="d-flex justify-content-center">
      <ava-icon
        iconName="Sparkles"
        [iconColor]="
          heroType === 'Marketplace' ? 'var(--ew-red-500)' : 'var(--star-color)'
        "
        class="me-3 ms-2 mt-3 stars-icon"
      ></ava-icon>
      <h1 class="greeting-title">
        {{ heroContent.greeting }} {{ userName ? userName : "" }}
      </h1>
    </div>
    <p class="greeting-message">
      {{ heroContent.message }}
    </p>
  </div>
  <div class="search-bar-background">
    <div class="search-bar-container transparent-bg">
      <app-search-bar
        [placeholderText]="placeholderText"
        (searchQueryChange)="onSearchQueryChange($event)"
        (sendClicked)="onSendClicked($event)"
      >
      </app-search-bar>
    </div>
  </div>
</div>
