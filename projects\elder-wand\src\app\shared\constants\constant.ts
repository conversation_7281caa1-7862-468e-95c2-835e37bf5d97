import { HeroContent, StudioCard } from '../interfaces/launchpad.interface';

export const HERO_TEXT_LOOKUP: Record<string, HeroContent> = {
  Sales: {
    greeting: 'Welcome',
    message:
      'Automate tasks and streamline your workflow with AI agents that boost productivity and handle the busywork for you.',
  },
  'Project Team': {
    greeting: 'Welcome back',
    message:
      "It's been a busy week, hasn't it? I've organized your space with the tasks you use most often, so you don't have to keep searching!",
  },
  Marketplace: {
    greeting: 'Welcome to Marketplace',
    message:
      'A curated list for you - browse or add more Agents & Flows to maximize value!',
  },
};

export const studiosCard: StudioCard[] = [
    {
      id: 1,
      title: 'Experience Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: 'images/experience_studio.png',
      link: 'https://aava-dev.avateam.io/experience/',
      isExternal: true,
    },
    {
      id: 2,
      title: 'Product Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: 'icons/product_studio.png',
      link: 'https://aava-dev.avateam.io/product/',
      isExternal: true,
    },
    {
      id: 3,
      title: 'Data Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: 'icons/data_studio.png',
      link: 'https://aava-dev.avateam.io/data',
      isExternal: true,
    },
    {
      id: 5,
      title: 'FinOps Studio',
      description: 'Evaluating design elements for accuracy and consistency',
      image: 'icons/finops_studio.png',
      link: 'https://aava-dev.avateam.io/finops',
      isExternal: true,
    },
  ];
