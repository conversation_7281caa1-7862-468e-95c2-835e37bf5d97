const { ModuleFederationPlugin } = require('webpack').container;
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');

module.exports = {
  output: {
    publicPath: 'auto',
    uniqueName: 'elder-wand',
    globalObject: 'self'
  },
  ignoreWarnings: [
    // Ignore Monaco Editor CommonJS dependency warnings
    /Module Warning \(from .*sass-loader.*\)/,
    /CommonJS or AMD dependencies can cause optimization bailouts/,
    /depends on '!.*style-loader.*'/,
  ],
  optimization: {
    runtimeChunk: false
  },
  experiments: {
    outputModule: true
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        // ✅ Include ALL Monaco css paths
        include: [
          /node_modules[\\\/]monaco-editor/,
          /node_modules[\\\/]monaco-editor[\\\/]esm/,
        ],
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.ttf$/,
        use: ['file-loader']
      }
    ]
  },
  resolve: {
    fallback: {
      path: false,
      fs: false,
    },
  },
  externals: {
    // Exclude Monaco Editor CommonJS dependencies from optimization warnings
    "style-loader/dist/runtime/injectStylesIntoStyleTag.js":
      "commonjs style-loader/dist/runtime/injectStylesIntoStyleTag.js",
    "style-loader/dist/runtime/insertBySelector.js":
      "commonjs style-loader/dist/runtime/insertBySelector.js",
    "style-loader/dist/runtime/insertStyleElement.js":
      "commonjs style-loader/dist/runtime/insertStyleElement.js",
    "style-loader/dist/runtime/setAttributesWithoutAttributes.js":
      "commonjs style-loader/dist/runtime/setAttributesWithoutAttributes.js",
    "style-loader/dist/runtime/styleDomAPI.js":
      "commonjs style-loader/dist/runtime/styleDomAPI.js",
    "style-loader/dist/runtime/styleTagTransform.js":
      "commonjs style-loader/dist/runtime/styleTagTransform.js",
  },
  plugins: [
    new ModuleFederationPlugin({
      name: 'elderWand',
      library: { type: 'module' },
      filename: 'remoteEntry.js',
      exposes: {
        './': './projects/elder-wand/src/app/app.component.ts',
      },
      shared: {
        '@angular/core': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
        '@angular/common': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
        '@angular/router': {
          singleton: true,
          strictVersion: true,
          requiredVersion: '^19.0.0',
        },
      },
    }),
    new MonacoWebpackPlugin({
      languages: ['python' , 'javascript' , 'typescript' , 'json' , 'sql' , 'html' , 'css' , 'markdown' , 'yaml' , 'xml' , 'plaintext'],
    }),
  ],
};