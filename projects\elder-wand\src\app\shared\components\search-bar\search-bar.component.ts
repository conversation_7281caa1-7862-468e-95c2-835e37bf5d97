import { Component, Output, EventEmitter, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '@ava/play-comp-library';

@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule, IconComponent],
})
export class SearchBar {
  @Output() searchQueryChange = new EventEmitter<string>();
  @Output() sendClicked = new EventEmitter<string>();
  @Input() placeholderText: string[] = [];
  searchValue: string = '';

  onSend() {
    if (this.searchValue.trim()) {
      this.searchQueryChange.emit(this.searchValue.trim());
      this.sendClicked.emit(this.searchValue.trim());
    }
  }

  clearSearch() {
    this.searchQueryChange.emit('');
  }
}
