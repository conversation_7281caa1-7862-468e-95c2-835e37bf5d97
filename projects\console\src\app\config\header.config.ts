import {
  HeaderConfig,
  SharedNavItem,
} from '@shared/components/app-header/app-header.component';
import { environment } from '../../environments/environment';

// Console specific navigation items
const consoleNavItems: SharedNavItem[] = [
  {
    label: 'Dashboard',
    route: '/',
    selected: false,
    hasDropdown: false,
    icon: `svgs/dashboard.svg`,
    disabled: true, // Dashboard link is disabled
  },
  {
    label: 'Build',
    route: '/build',
    selected: true, // Default to Build being selected since it's now the default route
    hasDropdown: true,
    dropdownOpen: false,
    icon: `svgs/launch.svg`,
    dropdownItems: [
      {
        label: 'Agents',
        description: 'Create, Manage and Edit Agents',
        route: '/build/agents',
        icon: `svgs/agents.svg`,
      },
      {
        label: 'Workflows',
        description: 'Create, Manage and Edit Workflows',
        route: '/build/workflows',
        icon: `svgs/workflows.svg`,
      },
    ],
  },
  {
    label: 'Libraries',
    route: '/libraries',
    selected: false,
    hasDropdown: true,
    dropdownOpen: false,
    icon: `svgs/libraries.svg`,
    dropdownItems: [
      {
        label: 'Prompts',
        description: 'Create, Manage and Edit Prompts',
        route: '/libraries/prompts',
        icon: `svgs/prompts.svg`,
      },
      {
        label: 'Models',
        description: 'Add, Manage and View Models',
        route: '/libraries/models',
        icon: `svgs/models.svg`,
      },
      {
        label: 'Knowledge-base',
        description: 'Add, Manage and Edit Knowledge-Base',
        route: '/libraries/knowledge-base',
        icon: `svgs/knowledgebase.svg`,
      },
      {
        label: 'Tools',
        description: 'Add, Manage and Edit Tools',
        route: '/libraries/tools',
        icon: `svgs/tools.svg`,
      },
      {
        label: 'Guardrails',
        description: 'Add, Manage and Edit Guardrails',
        route: '/libraries/guardrails',
        icon: `svgs/guardrails.svg`,
      },
    ],
  },
  {
    label: 'Approvals',
    route: '/approval',
    selected: false,
    hasDropdown: false,
    dropdownOpen: false,
    icon: `svgs/admin-management.svg`,
    disabled: false,
    dropdownItems: [
      {
        label: 'Approval Agents',
        description: 'Manage approval agents',
        route: '/approval/approval-agents',
        icon: `svgs/icons/awe_agents.svg`,
      },
      {
        label: 'Approval Workflows',
        description: 'Manage approval workflows',
        route: '/approval/approval-workflows',
        icon: `svgs/icons/awe_workflows.svg`,
      },
      {
        label: 'Approval Tools',
        description: 'Manage approval tools',
        route: '/approval/approval-tools',
        icon: `svgs/icons/awe_tools.svg`,
      },
    ],
  },
  {
    label: 'Manage',
    route: '/manage',
    selected: false,
    hasDropdown: true,
    dropdownOpen: false,
    icon: `svgs/manage.svg`,
    dropdownItems: [
      {
        label: 'Users & Admins Management',
        description:
          'Add, Manage Users and Admins, Modify tokens and assign filters',
        route: '/manage/admin-management',
        icon: `svgs/admin-management.svg`,
      },
      {
        label: 'Realm Management',
        description: 'Approve, Modify submission for Admins',
        route: '/manage/realm-management',
        icon: `svgs/realm-management.svg`,
      },
    ],
  },
  {
    label: 'Analytics',
    route: '/analytics',
    selected: false,
    hasDropdown: true,
    dropdownOpen: false,
    icon: `svgs/analytics.svg`,
    disabled: true,
    dropdownItems: [
      {
        label: 'Agents',
        description: '',
        route: '/analytics',
        icon: `svgs/analytics.svg`,
      },
    ],
  },
];

const availableStudioApps = [
  {
    name: 'Product Studio',
    route: environment.productStudioUrl,
    icon: 'svgs/product-studio.svg',
    description: 'Product strategy and business model canvas',
  },
  {
    name: 'Experience Studio',
    route: environment.experienceStudioUrl,
    icon: 'svgs/experience-studio.svg',
    description: 'AI-powered design analysis and code generation',
  },
  {
    name: 'Launchpad',
    route: environment.elderWandUrl,
    icon: 'svgs/launchpad.svg',
    description: 'Central application launcher and hub',
  },
];

// Console header configuration
export const consoleHeaderConfig: HeaderConfig = {
  logoSrc: 'public/header-ascendion-logo.svg',
  navItems: consoleNavItems,
  showOrgSelector: true,
  showThemeToggle: false,
  showAppDrawer: true,
  showProfileDropdown: true,
  showThemeToggleInProfile: true,
  disableThemeToggle: true,
  disableLanguageChange: true,
  showLanguageSwitcher: true,
  availableLanguages: [
    { code: 'en', name: 'English' },
    { code: 'fil', name: 'Filipino' },
    { code: 'es', name: 'Español' },
  ],
  currentApp: 'Console',
  availableApps: availableStudioApps,
  projectName: 'Console',
  redirectUrl: '/',
  // Logo animation configuration
  enableLogoAnimation: true,
  logoAnimationInterval: 4000, // 4 seconds between transitions
  logoAnimationStyle: 'fade',
  studioLogos: ['svgs/ascendion.svg', 'svgs/aava-logo.svg', 'svgs/console.svg'],
  studioNames: [
    'Console Studio',
    'Experience Studio',
    'Product Studio',
    'Launchpad',
  ],
};
