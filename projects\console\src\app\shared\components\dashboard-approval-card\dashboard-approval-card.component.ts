import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  ApprovalCardComponent,
  AvaTagComponent,
  IconComponent,
  ButtonComponent,
} from '@ava/play-comp-library';

@Component({
  selector: 'app-dashboard-approval-card',
  imports: [
    ApprovalCardComponent,
    AvaTagComponent,
    IconComponent,
    ButtonComponent,
  ],
  templateUrl: './dashboard-approval-card.component.html',
  styleUrl: './dashboard-approval-card.component.scss',
})
export class DashboardApprovalCardComponent {
  @Input() title = '';
  @Input() avatarUrl = 'assets/images/avatar.png';
  @Input() date = '';
  @Input() email = '';
  @Input() type = '';
  @Output() testClick = new EventEmitter<null>();
  @Output() sendBackClick = new EventEmitter<null>();
  @Output() approveClick = new EventEmitter<null>();

  approvalBtnCustomStyles: Record<string, string> = {
    background: 'linear-gradient(130.87deg, #0084FF 33.91%, #03BDD4 100%)',
  };

  onApproveClick() {
    this.approveClick.emit();
  }

  onSendBackClick() {
    this.sendBackClick.emit();
  }

  onTestClick() {
    this.testClick.emit();
  }
}
