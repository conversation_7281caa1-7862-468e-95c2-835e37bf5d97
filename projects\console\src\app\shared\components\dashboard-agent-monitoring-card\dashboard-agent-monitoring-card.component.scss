::ng-deep #aget-monitoring-card {
  .dashboard-txt-card-2 {
    .ava-text-card-wrapper {
      .ava-default-card-container {
        .ava-default-card.default-card.card {
          border: none;
          box-shadow: none;
          height: 105px;
          width: 100%;
          padding: 16px;
          background-color: #ffffff;
          border: 1px solid #d1d3d8;

          .section-wrapper {
            display: flex;
            flex-direction: column;
            gap: 24px;
          }

          .section-1 {
            display: flex;
            justify-content: space-between;
            gap: 10px;

            .name {
              flex: 1 1 auto;

              h1 {
                margin: 0px;
                font-family: Mulish;
                font-weight: 600;
                font-style: SemiBold;
                font-size: 20px;
                color: #3b3f46;
                max-width: 400px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                display: inline-block;
              }
            }

            .status {
              width: 70px;
              font-family: Mulish;
              font-weight: 600;
              font-style: SemiBold;
              font-size: 12px;
              display: flex;
              align-items: center;
              gap: 8px;
              color: #3b3f46;

              .active-icon {
                display: inline-block;
                height: 16px;
                width: 16px;
                background-color: #059669;
                text-align: center;
                font-size: 48px;
                border-radius: 50%;
              }
            }
          }

          .section-2 {
            display: flex;
            column-gap: 54px;

            span {
              font-family: "Mulish";
              font-weight: 400;
              font-style: normal;
              font-size: 16px;
              color: #3b3f46;
              max-width: 150px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              display: inline-block;
            }

            ava-icon {
              button {
                height: 24px;
                width: 24px;
                background-color: #f0f1f2;
                width: 24px;
                height: 24px;
                border-radius: 20px;
                padding: 4px;
              }
            }
          }
        }
      }
    }
  }
}
