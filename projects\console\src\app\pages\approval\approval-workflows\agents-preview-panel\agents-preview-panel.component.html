<div class="preview-panel">
  <div class="backdrop" (click)="closePreview()"></div>
<app-preview-panel class="panel-container" [divider]="false" (click)="$event.stopPropagation()">
<div panel-header class="preview-header">
  <span class="panel-title">Metadata Information</span>
  <ava-icon iconName="x" iconColor="black" class="close-btn" (click)="closePreview()"></ava-icon>
</div>
<div panel-content class="preview-content">
  <!-- Loading State -->
  <div *ngIf="previewData?.loading" class="preview-loading">
    <div class="loading-spinner"></div>
    <p>Loading details...</p>
  </div>

  <!-- Content based on preview data -->
  <div *ngIf="previewData?.data && !previewData?.loading">
    <!-- Model Preview -->
    <div *ngIf="previewData?.type === 'workflow'" class="model-preview">
      <div class="model-section">
        <h3>Workflow Details</h3>
        
        <div class="model-field" *ngIf="previewData?.title || previewData.data.name">
          <label>Workflow Name</label>
          <div class="field-value">{{ previewData?.title || previewData.data.name }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.modelDescription || previewData.data.description">
          <label>Description</label>
          <div class="field-value description-text">{{ previewData.data.modelDescription || previewData.data.description }}</div>
        </div>

        <div class="model-meta">
          <div class="meta-row">
            <div class="meta-item" *ngIf="previewData.data.createdBy">
              <label>Added by</label>
              <div class="field-value">{{ previewData.data.createdBy}}</div>
            </div>
            <div class="meta-item" *ngIf="previewData.data.createdOn || previewData.data.createdAt">
              <label>Added on</label>
              <div class="field-value">{{ previewData.data.createdOn || previewData.data.createdAt | date:'MM/dd/yyyy' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="previewData?.error" class="preview-error">
    <p>{{ previewData.error }}</p>
  </div>
</div>
 <div panel-footer>
  <div class="footer-buttons-row">
    <ava-button [label]="getButtonLabel()" variant="primary" size="small" (userClick)="onButtonClick($event)"
    state="default" iconName="edit" iconPosition="left"
    ></ava-button>
    <ava-button [label]="labels.test" (userClick)="handleTest()" variant="secondary" size="small" [customStyles]="{
          'border': '2px solid transparent',
          'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
          'background-origin': 'border-box',
          'background-clip': 'padding-box, border-box',
          '--button-effect-color': '33, 90, 214'
        }" state="default" iconName="play" iconPosition="left"></ava-button>
    <ava-button [label]="labels.sendback" (userClick)="handleSendback()" variant="secondary" size="small"
      [customStyles]="{
          'border': '2px solid transparent',
          'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
          'background-origin': 'border-box',
          'background-clip': 'padding-box, border-box',
          '--button-effect-color': '33, 90, 214'
        }" state="default" iconName="move-left" iconPosition="left"></ava-button>
    <ava-button [label]="labels.approve" (userClick)="handleApprove()" variant="primary" size="small"
      [customStyles]="{
          background:
            'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
          '--button-effect-color': '33, 90, 214',
        }" state="default" iconName="Check" iconPosition="left"></ava-button>
  </div>
  </div>
</app-preview-panel>
</div>
