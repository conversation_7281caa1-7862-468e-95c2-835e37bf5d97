import {
  ApplicationConfig,
  importProv<PERSON>sFrom,
  provideZoneChangeDetection,
} from '@angular/core';
import { provideRouter } from '@angular/router';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { routes } from './app.routes';
import { AuthInterceptor } from '@shared/auth/interceptors/auth.interceptor';
import {
  LucideAngularModule,
  Hammer,
  User,
  Settings,
  Info,
  ChevronLeft,
  ChevronRight,
  ShieldAlert,
  Hourglass,
  CircleCheck,
  XCircle,
  AlignVerticalDistributeStart,
  CircleCheckBig,
  MoveLeft,
  Play,
  CalendarDays,
  EllipsisVertical,
  SquarePen,
  Wifi,
  Search,
  AlertCircle,
  EyeOff,
  Mail,
  Phone,
  Check,
  X,
  Edit,
  Trash,
  Plus,
  Minus,
  ChevronDown,
  ChevronUp,
  Eye,
  Home,
  Layout,
  Bell,
  Grid,
  Star,
  Leaf,
  CheckCircle,
  AlertTriangle,
  XOctagon,
  Sparkles,
  Slash,
  Feather,
  Globe,
  Send,
  Box,
  Paperclip,
  Bot,
  Archive,
  Copy,
  Trash2,
  Users,
  Wrench,
  TrendingUp,
  PanelLeft,
  BookOpen,
  NotebookText,
  Redo,
  RotateCcw,
  Swords,
  Undo,
  Pencil,
  RotateCw,
  SendHorizontal,
  WandSparkles,
  MousePointer2,
  Hand,
  ZoomIn,
  ZoomOut,
  Clock,
  CircleX,
  FileText,
  Download,
  Save,
  Workflow,
  List,
  ShieldCheck,
  BookText,
  RefreshCw,
  Languages,
  Sun,
  Text,
  Code,
  CloudUpload,
  Lightbulb,
  Database,
  DollarSign,
  ArrowUpRight,
  ChartSpline,
  MessageSquare,
  MoveRight,
  LayoutGrid,
  ArrowLeft,
  ChartColumnIncreasing,
  Upload
} from 'lucide-angular';
import { MarkdownModule } from 'ngx-markdown';
import { environment } from '../environments/environment';
import { AGENT_ENVIRONMENT_CONFIG, ENVIRONMENT_CONFIG } from '@shared';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: AGENT_ENVIRONMENT_CONFIG,
      useValue: {
        consoleApi: environment.consoleApi,
        consoleApiV2: environment.consoleApiV2,
      },
    },
    {
      provide: ENVIRONMENT_CONFIG,
      useValue: {
        consoleApi: environment.consoleApi,
        consoleApiV2: environment.consoleApiV2,
        consoleEmbeddingApi: environment.consoleEmbeddingApi,
        consoleInstructionApi: environment.consoleInstructionApi,
        baseUrl: environment.baseUrl,
        apiUrl: environment.consoleApi,
      },
    },
    importProvidersFrom(
      LucideAngularModule.pick({
        Hammer,
        User,
        Settings,
        Info,
        ChevronLeft,
        ChevronRight,
        ShieldAlert,
        Hourglass,
        CircleCheck,
        XCircle,
        AlignVerticalDistributeStart,
        CircleCheckBig,
        MoveLeft,
        Play,
        CalendarDays,
        EllipsisVertical,
        SquarePen,
        Wifi,
        Search,
        AlertCircle,
        EyeOff,
        Mail,
        Phone,
        Check,
        X,
        Edit,
        Trash,
        Plus,
        Minus,
        ChevronDown,
        ChevronUp,
        Eye,
        Home,
        Layout,
        Bell,
        Grid,
        Star,
        Leaf,
        CheckCircle,
        AlertTriangle,
        XOctagon,
        Sparkles,
        Slash,
        Feather,
        Globe,
        Send,
        Box,
        Paperclip,
        Bot,
        Wrench,
        Users,
        MessageSquare,
        FileText,
        Archive,
        Copy,
        Trash2,
        TrendingUp,
        PanelLeft,
        BookOpen,
        NotebookText,
        Redo,
        RotateCcw,
        Swords,
        Undo,
        Pencil,
        RotateCw,
        SendHorizontal,
        WandSparkles,
        MousePointer2,
        Hand,
        ZoomIn,
        ZoomOut,
        Clock,
        CircleX,
        Download,
        Save,
        Workflow,
        List,
        ShieldCheck,
        BookText,
        RefreshCw,
        Languages,
        Sun,
        Text,
        Code,
        CloudUpload,
        Lightbulb,
        Database,
        DollarSign,
        ArrowUpRight,
        ChartSpline,
        MoveRight,
        LayoutGrid,
        ArrowLeft,
        ChartColumnIncreasing,
        Upload
      }),
      MarkdownModule.forRoot(),
    ),
  ],
};
