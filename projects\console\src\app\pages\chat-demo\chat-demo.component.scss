.chat-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  h1 {
    margin: 0;
    font-size: 24px;
    color: #333;
  }
}

.chat-wrapper {
  height: 500px;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.reset-button {
  padding: 8px 16px;
  background-color: #6566cd;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #5051b7;
  }
}

.demo-info {
  margin-top: 24px;
  padding: 24px;
  background-color: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  
  h2 {
    margin-top: 0;
    font-size: 18px;
    color: #333;
    margin-bottom: 16px;
  }
  
  p {
    margin-bottom: 16px;
    line-height: 1.5;
    color: #555;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
      color: #555;
    }
  }
}

@media (max-width: 768px) {
  .chat-demo-container {
    padding: 16px;
  }
  
  .chat-wrapper {
    height: 400px;
  }
  
  .demo-header h1 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .chat-wrapper {
    height: 350px;
  }
  
  .demo-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}