<div id="realms-container" class="container-fluid">
  <div id="search-filter-container" class="row g-3">
    <div class="col-12 col-md-8 col-lg-9 col-xl-10 search-section">
      <form [formGroup]="searchForm">
        <ava-textbox
          placeholder='Search "Realms"'
          hoverEffect="glow"
          pressedEffect="solid"
          formControlName="search"
        >
          <ava-icon
            slot="icon-start"
            iconName="search"
            [iconSize]="16"
            iconColor="var(--color-brand-primary)"
          >
          </ava-icon>
        </ava-textbox>
      </form>
    </div>
    <div class="col-12 col-md-4 col-lg-3 col-xl-2 action-buttons">
      <ava-button label="Create" variant="primary" size="large" (userClick)="onCreateRealmPopup()" [width]="'100%'"
        class="action-button">
      </ava-button>
    </div>
  </div>

  <div id="realms-card-container" class="row g-3">
    <ava-text-card
      class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
      [type]="'create'"
      [iconName]="'plus'"
      iconColor="#144692"
      title="Create Realm"
      (cardClick)="onCreateRealmPopup()"
      [isLoading]="isLoading"
    >
    </ava-text-card>

    <!-- No Results Message -->
    <div
      class="col-12 d-flex justify-content-center align-items-center py-5"
      *ngIf="!isLoading && filteredRealms.length === 0"
    >
      <div class="text-center">
        <h5 class="text-muted">No realms found matching your criteria</h5>
      </div>
    </div>

    <ng-container
      *ngFor="
        let realm of isLoading && displayedRealms.length === 0
          ? cardSkeletonPlaceholders
          : displayedRealms
      "
    >
      <ava-console-card
        class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 col-xxl-2 mt-5"
        [title]="realm?.title"
        categoryIcon="earth"
        categoryTitle="Realm"
        categoryValue="2"
        [author]="realm?.owner || 'AAVA'"
        [actions]="defaultActions"
        (actionClick)="onActionClick($event, realm.id)"
        [skeleton]="isLoading"
      >
        @if(displayedRealms.length) {
          <div class="tag--container">
            @for(tag of realm.tags; track tag){
              <ava-tag [label]="tag" [customStyle]="{ background: '#E6F3FF', color: '#292C3D' }"
                 size="sm" iconColor="#000000"></ava-tag>
              }
          </div>
        }
      </ava-console-card>
    </ng-container>
  </div>

  <!-- Pagination Footer -->
  <div class="row" *ngIf="filteredRealms.length > 0">
    <div class="col-12 d-flex justify-content-center mt-4">
      <app-page-footer
        [totalItems]="filteredRealms.length + 1"
        [currentPage]="currentPage"
        [itemsPerPage]="itemsPerPage"
        (pageChange)="onPageChange($event)"
      ></app-page-footer>
    </div>
  </div>
</div>

<!-- Success Popup -->
<ava-popup
  [show]="showSuccessPopup"
  [title]="popupTitle"
  [message]="popupMessage"
  [showHeaderIcon]="true"
  headerIconName="{{ iconName }}"
  iconColor="#28a745"
  [showClose]="true"
  [showCancel]="false"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#28a745'"
  (confirm)="onSuccessConfirm()"
  (closed)="closeSuccessPopup()"
>
</ava-popup>

<!-- Delete Confirmation Popup -->
<ava-popup
  [show]="showDeletePopup"
  title="Delete Realm?"
  [message]="
    'Are you sure you want to delete ?'
  "
  [showHeaderIcon]="true"
  headerIconName="trash"
  iconColor="#dc3545"
  [showClose]="true"
  [showCancel]="true"
  [showConfirm]="true"
  [confirmButtonLabel]="'Delete'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="onConfirmDelete()"
  (cancel)="closeDeletePopup()"
  (closed)="closeDeletePopup()"
>
</ava-popup>

<ava-popup [show]="showRealmPopup" [showTitle]="false" [showHeaderIcon]="false" [showClose]="false" [showInlineMessage]="false"
    [showConfirm]="false" message="" [showCancel]="false" [popupWidth]="'744px'">

    <div class="add__realm--container">
        <form [formGroup]="addRealmForm" class="realm__form">
            <div class="form-fields">
                <div class="input__field--wrapper">
                    <label class="filter-label required">Name of the Realm</label>
                    <ava-textbox class="input-field" formControlName="realmName" id="realmName" name="realmName"
                        placeholder="Enter Realm Name" [required]="true"
                        [fullWidth]="false" size="md">
                    </ava-textbox>
                </div>
                <div class="input__field--wrapper"></div>
            </div>

            <div class="form-fields">
                <div class="input__field--wrapper">
                    <label class="filter-label required">Choose Organization</label>
                    <ava-dropdown [dropdownTitle]="'Select Organization'" [options]="orgOptions"
                        [selectedValue]="selectedOrgName" [disabled]="false" (selectionChange)="onOrgSelect($event)"
                        [search]="true" [enableSearch]="true"></ava-dropdown>
                </div>
                <div class="input__field--wrapper">
                    <label class="filter-label required">Choose Domain</label>
                    <ava-dropdown [dropdownTitle]="'Select Domain'" [options]="domainOptions"
                        [selectedValue]="selectedDomainName" [disabled]="!selectedOrg"
                        (selectionChange)="onDomainSelect($event)" [search]="true" [enableSearch]="true"></ava-dropdown>
                </div>
            </div>

            <div class="form-fields">
                <div class="input__field--wrapper">
                    <label class="filter-label required">Choose Project</label>
                    <ava-dropdown [dropdownTitle]="'Select Project'" [options]="projectOptions"
                        [selectedValue]="selectedProjectName" [disabled]="!selectedDomain"
                        (selectionChange)="onProjectSelect($event)" [search]="true"
                        [enableSearch]="true"></ava-dropdown>
                </div>
                <div class="input__field--wrapper">
                    <label class="filter-label required">Choose Team</label>
                    <ava-dropdown [dropdownTitle]="'Select Team'" [options]="teamOptions"
                        [selectedValue]="selectedTeamName" [disabled]="!selectedProject"
                        (selectionChange)="onTeamSelect($event)" [search]="true" [enableSearch]="true"></ava-dropdown>
                </div>
            </div>
        </form>
        <div class="button__container">
            <ava-button label="Cancel" variant="secondary" size="large" (userClick)="closeRealmPopup()" [width]="'100%'"
                class="action-button">
            </ava-button>
            <ava-button [label]="isRealmUpdateMode ? 'Update' : 'Create'" variant="primary" size="large" (userClick)="createRealm()" [width]="'100%'"
                class="action-button" [disabled]="!addRealmForm.valid">
            </ava-button>
        </div>
    </div>
</ava-popup>
