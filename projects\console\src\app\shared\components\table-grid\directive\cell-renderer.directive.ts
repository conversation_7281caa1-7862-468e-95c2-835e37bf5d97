import { ComponentRef, computed, DestroyRef, Directive, effect, inject, input, signal, ViewContainerRef } from '@angular/core';
import { CellRenderer, CellRendererParams } from '../model/table-grid.model';

@Directive({
  selector: '[aweCellRenderer]',
  standalone: true,
})
export class CellRendererDirective {
  cellRenderer = input<any>();
  value = input<any>();
  rowData = input<any>();
  colDef = input<any>();
  rowIndex = input<number>();

  private viewContainerRef = inject(ViewContainerRef);
  private destroyRef = inject(DestroyRef);

  private componentRef = signal<ComponentRef<any> | null>(null)
  
  private cellParams = computed(() => {
    const baseParams: CellRendererParams  = {
      value: this.value(),
      rowData: this.rowData(),
      colDef: this.colDef(),
      rowIndex: this.rowIndex(),
    }
    const rendererParams = this.colDef()?.cellRendererParams || {};
    return { ...baseParams, ...rendererParams };
  }
);

  private updateEffect = effect(() => {
    const renderer = this.cellRenderer();
    const params = this.cellParams();
    
    if (renderer) {
      this.initializeComponent(renderer, params);
    } else {
      this.destroyComponent();
    }
  });

  private initializeComponent(renderer: any, params: CellRendererParams): void {
    const currentRef = this.componentRef();
    
    if (!currentRef || currentRef.componentType !== renderer) {
      this.destroyComponent();
      this.createNewComponent(renderer, params);
    } else {
      this.updateExistingComponent(currentRef, params);
    }
  }

  private createNewComponent(renderer: any, params: CellRendererParams): void {
    try {
      const componentRef = this.viewContainerRef.createComponent(renderer);
      const instance = componentRef.instance as CellRenderer;

      if (instance && typeof instance.aweInit === 'function') {
        instance.aweInit(params);
      }
      this.componentRef.set(componentRef);
      componentRef.onDestroy(() => {
        if (this.componentRef() === componentRef) {
          this.componentRef.set(null);
        }
      });
      
    } catch (error) {
      console.error('Failed to create cell renderer component:', error);
    }
  }

  private updateExistingComponent(componentRef: ComponentRef<any>, params: CellRendererParams): void {
    if (componentRef.instance && typeof componentRef.instance.aweInit === 'function') {
      componentRef.instance.aweInit(params);
    }
  }

  private destroyComponent(): void {
    const currentRef = this.componentRef();
    if (currentRef) {
      currentRef.destroy();
      this.componentRef.set(null);
    }
    this.viewContainerRef.clear();
  }
}
