import { Component, OnInit } from '@angular/core';
import { Agent } from '../../shared/interfaces/agent-list.interface';
import { EntityService } from '../../shared/services/entity.service';
import { RevelioSearchResult } from '../../shared/services/search.service';
import {
  AgentCardAction,
  AgentCardComponent,
} from '../../shared/components/agent-card/agent-card.component';
import { CommonModule } from '@angular/common';
import { FilterTabsComponent } from '../../shared/components/filter-tabs/filter-tabs.component';
import { Hero } from '../../shared/components';
import { FilterTab } from '../../shared/interfaces/launchpad.interface';

@Component({
  selector: 'app-agents-filter',
  imports: [Hero, AgentCardComponent, CommonModule, FilterTabsComponent],
  templateUrl: './agents-filter.component.html',
  styleUrl: './agents-filter.component.scss',
})
export class AgentsFilterComponent implements OnInit {
  placeholderText: string[] = [
    'Search for the agents here',
    'Search for the agents here',
  ];
  agentsData: Agent[] = [];
  searchResults: RevelioSearchResult[] = [];
  searchQuery: string = '';
  isSearchLoading: boolean = false;
  isLoading: boolean = false;
  isMarketplace: boolean = true; // Set to false for dashboard view
  showTwoColumns: boolean = false;
  activeFilter = 'all';
  filterTabs: FilterTab[] = [
    { id: 'all', label: 'All', priority: 100, disabled: false },
    {
      id: 'experience',
      label: 'Experience Studio',
      icon: 'lightbulb',
      priority: 90,
      disabled: true,
    },
    {
      id: 'product',
      label: 'Product Studio',
      icon: 'box',
      priority: 80,
      disabled: true,
    },
    {
      id: 'data',
      label: 'Data Studio',
      icon: 'database',
      priority: 70,
      disabled: true,
    },
    {
      id: 'finops',
      label: 'FinOps Studio',
      icon: 'dollar-sign',
      priority: 60,
      disabled: true,
    },
  ];

  constructor(private entityService: EntityService) {}

  ngOnInit() {
    this.loadAgents();
  }

  onAgentClick(agent: Agent): void {
    console.log('Agent clicked:', agent);
    // Handle agent card click - navigate to details, etc.
  }

  onFilterChange(filterId: any) {
    this.activeFilter = filterId;
    // Filter agents by studio type if not 'all'
    if (filterId === 'all') {
      this.agentsData = [...this.agentsData];
    } else {
      const studioMap: any = {
        experience: 'Experience Studio',
        product: 'Product Studio',
        data: 'Data Studio',
        finops: 'FinOps Studio',
      };
      this.agentsData = this.agentsData.filter(
        (agent: Agent) => agent.studio?.type === studioMap[filterId],
      );
    }
  }

  /**
   * Load agents and convert entity data to agent format
   */
  private loadAgents(): void {
    this.isLoading = true;
    this.entityService.getAgents(0, 50).subscribe({
      next: (entityAgents) => {
        // Convert entity agents to the expected agent format
        this.agentsData = entityAgents.map((entityAgent) => ({
          id: String(entityAgent.id),
          title: entityAgent.name,
          description: entityAgent.description,
          rating: 4.5, // Default rating since it's not in the API
          studio: {
            name: 'Experience Studio', // Default studio
            type: 'Experience Studio',
            backgroundColor: '#FFF4F9',
            textColor: '#DC047B',
          },
          users: Math.floor(Math.random() * 100) + 10, // Random users count for now
        }));
      },
      error: (error) => {
        console.error('Error loading agents:', error);
        // Fallback to empty array
        this.agentsData = [];
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  /**
   * Handle search results from the search bar
   */
  onSearchResultsChange(results: RevelioSearchResult[]): void {
    this.searchResults = results;
  }

  /**
   * Handle search query change from the search bar
   */
  onSearchQueryChange(query: string): void {
    this.searchQuery = query;
  }

  getSkeletonCards(): number[] {
    return Array(8)
      .fill(0)
      .map((_, i) => i);
  }

  onAgentAction(action: AgentCardAction): void {
    console.log('Agent action:', action);

    switch (action.type) {
      case 'view':
        break;
      case 'edit':
        break;
      case 'delete':
        break;
      case 'play':
        break;
    }
  }

  /**
   * Handle search loading change from the search bar
   */
  onSearchLoadingChange(isLoading: boolean): void {
    this.isSearchLoading = isLoading;
  }

  /**
   * Handle send clicked from the search bar
   */
  onSendClicked(query: string): void {
    console.log('Search query sent:', query);
    // You can add additional logic here if needed
  }
}
