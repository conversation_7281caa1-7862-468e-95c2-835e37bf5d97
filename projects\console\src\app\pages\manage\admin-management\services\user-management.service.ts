import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'projects/console/src/environments/environment';
import { Subject, takeUntil } from 'rxjs';

export interface SearchParams {
  page: number;
  records: number;
  role?: string;
  searchValue?: string;
}
@Injectable({
  providedIn: 'root'
})
export class UserManagementService {
  private apiAuthUrl = environment.consoleApiAuthUrl;
  private cancelPreviousRequest$ = new Subject<void>();

  private http = inject(HttpClient); 

  getAllUsers(params: SearchParams) {
    // Cancel previous ongoing request
    this.cancelPreviousRequest$.next();
    
    let httpParams = new HttpParams()
      .set('page', params.page)
      .set('records', params.records);
    
    if (params.searchValue) {
      httpParams = httpParams.set('emailFilter', params.searchValue);
    }

    if (params.role) {
      httpParams = httpParams.set('roleFilter', params.role);
    }
    const url = `${this.apiAuthUrl}/user/mgmt`;

    return this.http.get(url, { params: httpParams }).pipe(
      takeUntil(this.cancelPreviousRequest$)
    );
  }

  getUserDetails(id: number) {
    const url = `${this.apiAuthUrl}/user?userId=${id}`;
    return this.http.get(url);
  }

  getAllRoles() {
    const url = `${this.apiAuthUrl}/roles`;
    return this.http.get(url);
  }

  getAllPages() {
    const url = `${this.apiAuthUrl}/pages`;
    return this.http.get(url);
  }

  getAllActions() {
    const url = `${this.apiAuthUrl}/actions`;
    return this.http.get(url);
  } 

  getAllRealms() {
    const url = `${this.apiAuthUrl}/realms`;
    return this.http.get(url);
  }
  
  getExistingAccessControl(roleId: string) {
    const url = `${this.apiAuthUrl}/access/permissions?roleId=${roleId}`;
    return this.http.get(url);
  }

  addNewUser(payload: any) {
    const url = `${this.apiAuthUrl}/user`;
    return this.http.post(url, payload);
  }

  updateUser(payload: any, userId: number) {
    const url = `${this.apiAuthUrl}/user?userId=${userId}`;
    return this.http.put(url, payload);
  }

  createRealm(name: string, teamId: string) {
    const url = `${this.apiAuthUrl}/realm?realmName=${name}&teamId=${teamId}`;
    return this.http.post(url, null);
  }

  createRole(payload: any) {
    const url = `${this.apiAuthUrl}/role`;
    return this.http.post(url, payload);
  }

  removeUser(id: string) {
    const url = `${this.apiAuthUrl}/user/${id}`;
    return this.http.delete(url);
  }
}
