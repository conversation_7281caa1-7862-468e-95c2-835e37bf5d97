.create-agent-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: calc(100vh - 200px);
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  color: var(--create-agent-title);
  margin-bottom: 30px;
  text-align: center;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
}

.agent-types-container {
  display: flex;
  justify-content: center;
  gap: 60px;
  width: 100%;
  max-width: 1200px;
  margin-top: 60px;

  ava-text-card ::ng-deep  .ava-text-card-container .prompt-type .text-card-header h3 {
    padding-left: 40px;
  }

  ava-text-card ::ng-deep .ava-text-card-container .prompt-type .ava-card-container .ava-card.card {
    height: 260px !important;
  }
}

/* Style the card when it's used with the agent-type-card class */
// :host ::ng-deep .agent-type-card {
//   background: var(--create-agent-card-bg);
//   backdrop-filter: blur(25px);
//   -webkit-backdrop-filter: blur(25px);
//   border-radius: 16px;
//   width: 400px;
//   min-height: 220px;
//   padding: 20px;
//   cursor: pointer;
//   transition: all 0.3s ease;
//   display: flex;
//   flex-direction: column;
//   justify-content: center;
//   align-items: center;
//   text-align: center;
//   box-shadow: var(--create-agent-card-shadow);
//   border: 1px solid var(--create-agent-card-border);
//   position: relative;
//   overflow: hidden;
  
//   &::before {
//     content: '';
//     position: absolute;
//     top: 0;
//     left: 0;
//     right: 0;
//     bottom: 0;
//     border-radius: 16px;
//     padding: 2px;
//     background: var(--create-agent-card-glow);
//     -webkit-mask: 
//       linear-gradient(#fff 0 0) content-box, 
//       linear-gradient(#fff 0 0);
//     -webkit-mask-composite: xor;
//     mask-composite: exclude;
//     pointer-events: none;
//     opacity: 0.5;
//   }
  
//   &:hover {
//     transform: translateY(-5px);
//     box-shadow: var(--create-agent-card-hover-shadow);
//     background: var(--create-agent-card-hover-bg);
//     border: 1px solid var(--create-agent-card-hover-border);
    
//     &::before {
//       opacity: 0.8;
//     }
//   }
// }

.agent-type-content {
  padding: 20px 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.agent-type-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 10px;
  margin-top: 20px;
  color: var(--create-agent-type-title);
  text-shadow: var(--create-agent-type-title-shadow);
  letter-spacing: 0.5px;
}

.agent-type-description {
  font-size: 14px;
  color: var(--create-agent-type-desc);
  line-height: 1.6;
  text-shadow: var(--create-agent-type-desc-shadow);
}

/* Responsive adjustments */
@media (max-width: 992px) {
  :host ::ng-deep .agent-type-card {
    width: 350px;
  }
}

@media (max-width: 768px) {
  .agent-types-container {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  .page-title {
    font-size: 28px;
  }
  
  :host ::ng-deep .agent-type-card {
    width: 100%;
    max-width: 400px;
  }
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
  :host ::ng-deep .agent-type-card {
    background: var(--create-agent-fallback-bg);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.1);
    
    &:hover {
      background: var(--create-agent-fallback-hover-bg);
      box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
    }
  }
}
