@use "../../../../assets/themes/mixins" as mixins;
@import "../../../../assets/themes/animate.scss";

#hero-container {
  width: 70%;
  margin: 0 auto;
  .stars-icon {
    animation: blink 3s ease-in-out infinite;
  }
  .greeting-title {
    @include mixins.gradient-text(
      linear-gradient(90deg, var(--ew-purple-700) 0%, var(--ew-red-300) 100%),
      text,
      transparent
    );
    text-align: center;
    font-weight: 700;
    font-size: 3rem;
  }
  .greeting-message {
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-caption);
    width: 100%;
    text-align: center;
    line-height: 1.5rem;
  }
  .search-bar-background {
    width: 100%;
    height: 4rem;
  }
  .marketplace-hero {
    .greeting-title {
      @include mixins.gradient-text(none, initial, initial);
      color: var(--black) !important;
    }
  }
}
