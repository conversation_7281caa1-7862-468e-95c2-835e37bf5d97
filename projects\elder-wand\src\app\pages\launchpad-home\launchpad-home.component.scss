@import "../../../assets/themes/animate.scss";

#launchpad-home-container {
  gap: 1rem;
  height: calc(100vh - 100px);
  .main-content-section {
    height: 100%;
    flex: 1;
    border-radius: 1.5rem;
    border: 2px solid var(--white);
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter: blur(16px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    background:
      linear-gradient(
        113.91deg,
        rgba(240, 235, 248, 0.8) 1.5%,
        rgba(255, 255, 255, 0.8) 50.17%,
        rgba(245, 233, 247, 0.8) 98.86%
      ),
      linear-gradient(0deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2));
    box-shadow: var(--Elevation-03set-2X) var(--Elevation-03set-2X)
      var(--Elevation-03set-2Blur) var(--Elevation-03set-2Spread)
      var(--Elevation-03set-2Color);

    &.full-width {
      max-width: calc(100vw - 2rem);
    }
    &.with-analytics {
      max-width: calc(100vw - 20rem);
    }
  }

  .analytics-toggle-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(
      180deg,
      var(--ew-red-50) 0%,
      var(--ew-violet-50) 100%
    );
    border: 2px solid var(--ew-red-300);
    box-shadow:
      0px 2px 2px -3px var(--build-agents-tool-border),
      0px 0px 6px -2px var(--color-border-default);
    backdrop-filter: blur(8px);
    cursor: pointer;
    transition: all 0.3s ease;
    &:hover {
      transform: scale(1.05);
    }
  }

  .content-section {
    p {
      text-align: center;
      color: var(--color-text-secondary);
      font-size: 20px;
      font-weight: 600;
      line-height: 24px;
    }
    .two-column-layout {
      width: 80%;
      .left-column {
        flex: 1;
      }
      .right-column {
        flex: 2;
      }
    }
  }

  .analytics-section {
    height: 100%;
    flex-shrink: 0;
    border-radius: 1.5rem;
    border: 2px solid var(--white);
    background:
      linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 255, 255, 0.2) 100%
      ),
      linear-gradient(
        114deg,
        rgba(240, 235, 248, 0.8) 1.5%,
        rgba(255, 255, 255, 0.8) 50.17%,
        rgba(245, 233, 247, 0.8) 98.86%
      );
    box-shadow:
      0px 2px 2px -3px var(--build-agents-tool-border),
      0px 0px 6px -2px var(--color-border-default);
    backdrop-filter: blur(8px);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    &.show {
      width: 288px;
      opacity: 1;
      transform: translateX(0);
    }
    &.hide {
      width: 0;
      opacity: 0;
      transform: translateX(100%);
    }

    .analytics-header .analytics-title-row {
      h2 {
        color: var(--black);
        font-size: 20px;
        font-weight: 600;
      }

      .close-btn {
        background: transparent;
        border: none;
        cursor: pointer;
        border-radius: 50%;
        transition: all 0.2s ease;
        &:hover {
          background: rgba(139, 92, 246, 0.1);
        }
        ava-icon {
          color: var(--ew-purple-600);
        }
      }
    }
  }
}
