export interface columnDefs {
  header: string,
  field: string,
  width?: number,
  minWidth?: number,
  maxWidth?: number,
  cellRenderer?: any;
  cellRendererParams?: { [key: string]: any };
}

export interface CellRenderer {
  aweInit?(params: CellRendererParams): void;
}

export interface CellRendererParams {
  value: any;
  rowData: any;
  colDef: any;
  rowIndex: number | undefined;
  context?: any;
}