import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'projects/console/src/environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ApprovalService {

  private baseUrl = environment.consoleApiV2;
  private baseUrlOg = environment.baseUrl;

  constructor(private http: HttpClient) {

  }

  public getAllReviewTools(page: number, records: number, isDeleted: boolean): Observable<any> {
    const url = `${this.baseUrl}/ava/force/da/userTools/review`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())
      .set('isDeleted', isDeleted.toString());

    return this.http.get<any>(url, { params });
  }

  public getAllReviewAgents(page: number, records: number, isDeleted: boolean): Observable<any> {
    const url = `${this.baseUrl}/ava/force/da/agent/review`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())
      .set('isDeleted', isDeleted.toString());

    return this.http.get<any>(url, { params });
  }

  public getAllReviewWorkflows(page: number, records: number, isDeleted: boolean): Observable<any> {
    const url = `${this.baseUrl}/ava/force/da/workflow/review`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())
      .set('isDeleted', isDeleted.toString());

    return this.http.get<any>(url, { params });
  }

  public getReviewsForTools(page: number, records: number, isDeleted: boolean): Observable<any> {
    const url = `${this.baseUrlOg}/tools/userTools/review`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())

    return this.http.get<any>(url, { params });
  }

  public getReviewsForAgents(page: number, records: number, isDeleted: boolean): Observable<any> {
    const url = `${this.baseUrlOg}/agents/review`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())

    return this.http.get<any>(url, { params });
  }

  public getReviewsForWorkflows(page: number, records: number, isDeleted: boolean): Observable<any> {
    const url = `${this.baseUrlOg}/workflows/review`;
    const params = new HttpParams()
      .set('page', page.toString())
      .set('records', records.toString())

    return this.http.get<any>(url, { params });
  }

  public approveTool(id: number, toolId: number, status: string, reviewedBy: string) {
      const url = `${this.baseUrlOg}/tools/userTools/approval`;
      const body = {
          id: id,
          status: status
      };

      return this.http.put(url, body);
  }

  public rejectTool(id: number, toolId: number, status: string, reviewedBy: string, message: string){
    // reject tool code
    const url = `${this.baseUrlOg}/tools/userTools/approval`;
    const body = {
      id: id,
      status: status
    };

    return this.http.put(url, body);
  }

  public approveAgent(id: number, agentId: number, status: string, reviewedBy: string) {
      const url = `${this.baseUrlOg}/agents/approval`;
      const body = {
          id: id,
          status: status
      };

      return this.http.put(url, body);
  }

  public approveAgentById(id: number, agentId: number, status: string, reviewedBy: string, entity: string) {
    const url = `${this.baseUrlOg}/agents/approval?entity=${entity}`;
    const body = {
        id: id,
        status: status
    };

    return this.http.put(url, body);
  }

  public rejectAgent(id: number, agentId: number, status: string, reviewedBy: string, message: string){
    // reject tool code
    const url = `${this.baseUrlOg}/agents/approval`;
    const body = {
      id: id,
      status: status
    };

    return this.http.put(url, body);
  }

  public rejectAgentById(id: number, agentId: number, status: string, reviewedBy: string, message: string, entity: string){
    // reject tool code
    const url = `${this.baseUrlOg}/agents/approval?entity=${entity}`;
    const body = {
      id: id,
      status: status
    };

    return this.http.put(url, body);
  }

  public approveWorkflow(id: number, workflowId: number, status: string, reviewedBy: string) {
      const url = `${this.baseUrlOg}/workflows/approval`;
      const body = {
          id: id,
          status: status
      };

      return this.http.put(url, body);
  }

  public rejectWorkflow(id: number, workflowId: number, status: string, reviewedBy: string, message: string){
    const url = `${this.baseUrlOg}/workflows/approval`;
    const body = {
      id: id,
      status: status
    };

    return this.http.put(url, body);
  }

  public getAgentsMetrics(){
    const url = `${this.baseUrlOg}/agents/metrics`;
    return this.http.get<any>(url);
  }

  public getToolsMetrics(){
    const url = `${this.baseUrlOg}/tools/userTools/metrics`;
    return this.http.get<any>(url);
  }

  public getWorkflowsMetrics(){
    const url = `${this.baseUrlOg}/workflows/metrics`;
    return this.http.get<any>(url);
  }
}