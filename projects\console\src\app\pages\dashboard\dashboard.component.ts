import { Component, OnInit } from '@angular/core';
import { CommonModule, formatDate } from '@angular/common';
import { RouterModule } from '@angular/router';
import { Router } from '@angular/router';
import { SharedApiServiceService } from '../../shared/services/shared-api-service.service';
import {
  AvaTagComponent,
  DialogButton,
  DialogService,
  DropdownComponent,
  DropdownOption,
  LinkComponent,
} from '@ava/play-comp-library';
import {
  ActivityMonitoringI,
  DashboardDetailI,
} from './models/dashboard.model';
import { ApprovalService } from '../../shared/services/approval.service';
import {
  ACTIVE_MONITORING_OPTIONS,
  ActiveMonitorings,
  APIKeys,
  DASHBOARD_CARD_DETAILS,
} from './constants/dashoard.constant';
import { RequestStatus } from '../approval/approval.component';
import approvalText from '../approval/constants/approval.json';
import { DashboardImgCardComponent } from '../../shared/components/dashboard-img-card/dashboard-img-card.component';
import { DashboardTxtCardComponent } from '../../shared/components/dashboard-txt-card/dashboard-txt-card.component';
import { DashboardAgentMonitoringCardComponent } from '../../shared/components/dashboard-agent-monitoring-card/dashboard-agent-monitoring-card.component';
import { DashboardApprovalCardComponent } from '../../shared/components/dashboard-approval-card/dashboard-approval-card.component';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    DropdownComponent,
    AvaTagComponent,
    LinkComponent,
    DashboardImgCardComponent,
    DashboardTxtCardComponent,
    DashboardAgentMonitoringCardComponent,
    DashboardApprovalCardComponent,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {
  options: DropdownOption[] = ACTIVE_MONITORING_OPTIONS;
  public labels: any = approvalText.labels;

  dashboardDetails: DashboardDetailI[] = [];
  // Dashboard metrics
  selectedActiveMonitoring = ActiveMonitorings.agents;
  activityMonitoringCount = 5;
  activityMonitoring: ActivityMonitoringI[] = [];
  workflowApprovals: any[] = [];
  approvalTabs: string[] = ['Agents', 'Workflow', 'Tools'];
  selectedApprovalTab: string = 'Agents';
  approvalData: any[] = []
  // seprating the tool and agents.
  activityMonitoringTools: ActivityMonitoringI[] = [];
  activityMonitoringAgents: ActivityMonitoringI[] = [];
  // Dashboard metrics
  totalAgents: number = 0;
  newAgentsCreated: number = 50;
  totalWorkflows: number = 124;
  newWorkflowsCreated: number = 50;
  totalUsers: number = 300;
  newUsersAdded: number = 50;

  constructor(
    private router: Router,
    private apiService: SharedApiServiceService,
    private approvalService: ApprovalService,
    private dialogService: DialogService
  ) { }

  ngOnInit(): void {
    this.loadCollabrativeAgentAnalytics()
    this.getAllReviewAgents();
  }

  setActiveMonitoringData() {
    // INsted of re-formating, just switch the values
    if (this.selectedActiveMonitoring === ActiveMonitorings.tools) {
      this.activityMonitoring = this.activityMonitoringTools;
    } else {
      this.activityMonitoring = this.activityMonitoringAgents;
    }
  }

  mapToActivityMonitoringItem = (
    item: any,
    nameKey: APIKeys,
    usageKey: APIKeys,
  ): ActivityMonitoringI => ({
    agentName: item[nameKey] || '',
    totalRuns: Number(item[usageKey]) || 0,
    status: '',
    user: '',
    date: '',
  });

  public toRequestStatus(value: string | null | undefined): RequestStatus {
    return value === 'approved' || value === 'rejected' || value === 'review'
      ? value
      : 'review';
  }

  loadCollabrativeAgentAnalytics() {
    const date = new Date();
    const dateEnd = this.apiService.formatDate(date);
    date.setDate(1);
    const dateStart = this.apiService.formatDate(date);
    this.apiService
      .getCollabrativeAgentAnalytics(dateStart, dateEnd)
      .subscribe((response: Record<string, any>) => {
        // setting dashboard card values
        this.dashboardDetails = DASHBOARD_CARD_DETAILS.map((cardDetail) => {
          cardDetail.value =
            (response[cardDetail.field] as number) || this.totalAgents;
          return cardDetail as DashboardDetailI;
        });

        // Active Monitoring
        // Extracting tools and agents to seprate varibales to reduce frequent re-formatig as the dropdown value changes
        this.activityMonitoringTools = (response[APIKeys.toolUsage] as any[])
          .slice(0, this.activityMonitoringCount)
          .map((toolUsage) =>
            this.mapToActivityMonitoringItem(
              toolUsage,
              APIKeys.toolName,
              APIKeys.usageCount,
            ),
          );

        this.activityMonitoringAgents = (
          response[APIKeys.agentMetrics] as any[]
        )
          .slice(0, this.activityMonitoringCount)
          .map((agentMetric) =>
            this.mapToActivityMonitoringItem(
              agentMetric,
              APIKeys.agentName,
              APIKeys.workflowCount,
            ),
          );

        this.setActiveMonitoringData();
      });

    this.approvalService.getAllReviewAgents(1, 100, false).subscribe((res) => {
      const agentReviewDetails = res?.agentReviewDetails || [];
      // Extracting agents which are under review
      this.totalAgents =
        agentReviewDetails.filter(
          (agentReviewDetail: any) => agentReviewDetail.status !== 'approved',
        )?.length || 0;

      // If this API call is late then will set approval count here.
      const toolCardDetial = this.dashboardDetails.at(-1);
      if (toolCardDetial) {
        toolCardDetial.value = this.totalAgents;
      }
    });

    this.approvalService
      .getAllReviewWorkflows(1, 5, false)
      .subscribe((response) => {
        const type = 'workflow';

        this.workflowApprovals = response.workflowReviewDetails?.map(
          (req: any) => {
            const statusIcons: Record<RequestStatus, string> = {
              approved: 'circle-check-big',
              rejected: 'circle-x',
              review: 'clock',
            };
            const statusTexts: Record<RequestStatus, string> = {
              approved: this.labels.approved,
              rejected: this.labels.rejected,
              review: this.labels.review,
            };
            const statusKey = this.toRequestStatus(req?.status);
            let specificId = 0;
            let title = '';

            specificId = req.workflowId;
            title = req.workflowName;

            return {
              id: req.id,
              refId: specificId,
              type: type,
              session1: {
                title: title,
                labels: [
                  {
                    name: type,
                    color: 'success',
                    background: 'red',
                    type: 'normal',
                  },
                  {
                    name: req.changeRequestType,
                    color:
                      req.changeRequestType === 'update' ? 'error' : 'info',
                    background: 'red',
                    type: 'pill',
                  },
                ],
              },
              session2: [
                {
                  name: type,
                  color: 'default',
                  background: 'red',
                  type: 'normal',
                },
                {
                  name: req.status,
                  color: 'default',
                  background: 'red',
                  type: 'normal',
                },
              ],
              session3: [
                {
                  iconName: 'user',
                  label: req.requestedBy,
                },
                {
                  iconName: 'calendar-days',
                  label: formatDate(req?.requestedAt, 'dd MMM yyyy', 'en-IN'),
                },
              ],
              session4: {
                status: statusTexts[statusKey],
                iconName: statusIcons[statusKey],
              },
            };
          },
        );
      });
  }

  onSelectionChange(data: any) {
    this.selectedActiveMonitoring = data.selectedValue;
    this.setActiveMonitoringData();
  }

  // Common pill base style
  private readonly basePillStyle: Record<string, string> = {
    'border-radius': '20px',
    padding: '0px 20px',
    height: '32px',
    display: 'flex',
    'justify-content': 'center',
  };

  // Custom pill style for Agent Approvals card
  agentApprovalsPillStyle: Record<string, string> = {
    ...this.basePillStyle,
    'background-color': '#fff',
    color: '#2D3036',
    border: '1px solid #2563EB',
  };

  // Selected state pill style
  agentApprovalsSelectedPillStyle: Record<string, string> = {
    ...this.basePillStyle,
    'background-color': '#2563EB',
    color: '#FFFFFF',
  };

  updatedSelectedApprovalTab(tab: string) {
    this.selectedApprovalTab = tab;
    if (tab == 'Agents') {
      this.getAllReviewAgents();
    } else if (tab == 'Workflow') {
      this.getAllReviewWorkflows();
    } else if (tab == 'Tools') {
      this.getAllReviewTools();
    }
  }

  getAllReviewAgents() {
    this.approvalService.getAllReviewAgents(1, 20, false).subscribe((response) => {
      this.approvalData = (response?.agentReviewDetails || [])
        .filter((item: any) => item.status !== 'approved' && item.status !== 'rejected')
        .slice(0, 3)
        .map((item: any) => ({
          id: item.id,
          title: item.agentName,
          requestedBy: item.requestedBy,
          requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),
          type: 'agent',
          rawData: item,
        }));
    });
  }

  getAllReviewWorkflows() {
    this.approvalService.getAllReviewWorkflows(1, 20, false).subscribe((response) => {
      this.approvalData = (response?.workflowReviewDetails || [])
        .filter((item: any) => item.status !== 'approved' && item.status !== 'rejected')
        .slice(0, 3)
        .map((item: any) => ({
          title: item.workflowName,
          requestedBy: item.requestedBy,
          requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),
          type: 'workflow',
          rawData: item,
        }));
    });
  }

  getAllReviewTools() {
    this.approvalService.getAllReviewTools(1, 20, false).subscribe((response) => {
      this.approvalData = (response?.userToolReviewDetails || [])
        .filter((item: any) => item.status !== 'approved' && item.status !== 'rejected')
        .slice(0, 3)
        .map((item: any) => ({
          title: item.toolName,
          requestedBy: item.requestedBy,
          requestedAt: formatDate(item.requestedAt, 'dd MMM yyyy', 'en-IN'),
          type: 'tool',
          rawData: item,
        }));
    });
  }

  onApproveClick(item: any) {
    this.handleApproval(item.type, item.rawData)
  }

  onSendBackClick(item: any) {
    this.showFeedbackDialog(item);
  }

  onTestClick(item: any) {
    const type = item.type as 'agent' | 'workflow' | 'tool'
    this.handleTesting(type, item.rawData)
  }

  goToPage(event: MouseEvent, route: string): void {
    event.preventDefault();
    event.stopPropagation();
    this.router.navigate([route]);
  }

  showFeedbackDialog(item: any): void {
    const customButtons: DialogButton[] = [
      { label: 'Cancel', variant: 'secondary', action: 'cancel' },
      { label: 'Send Back', variant: 'primary', action: 'sendback' }
    ];

    const entityNameMap = {
      agent: 'Agent',
      workflow: 'Workflow',
      tool: 'Tool'
    };
    const type = item.type as 'agent' | 'workflow' | 'tool';
    const entityName = entityNameMap[type];

    const contextMessages = {
      title: 'Confirm Send Back',
      message: `This ${entityName.toLowerCase()} will be sent back for corrections. Kindly provide the necessary feedback.`
    };

    if (!contextMessages) return;

    this.dialogService.feedback({
      title: contextMessages.title,
      message: contextMessages.message,
      buttons: customButtons,
      variant: 'info'
    }).then(result => {
      if (result.confirmed === true) {
        this.handleRejection(type, result.data, item.rawData);
      }
    });
  }

  handleRejection(type: 'agent' | 'workflow' | 'tool', feedback: any, item: any): void {
    const id = item.id;
    const reviewedBy = item.reviewedBy;
    const message = feedback;
    const status = 'rejected';
    const entityId = this.getEntityId(type, item);
    const contextConfig = {
      agent: {
        title: 'Rejecting Agent...',
        successTitle: 'Agent Rejected',
        successMessage: this.labels.agentSuccessRejectMessage,
        apiCall: () => this.approvalService.rejectAgent(id, entityId, status, reviewedBy, message),
        refresh: () => { this.getAllReviewAgents(); this.loadCollabrativeAgentAnalytics(); }
      },
      workflow: {
        title: 'Rejecting Workflow...',
        successTitle: 'Workflow Rejected',
        successMessage: this.labels.workflowSuccessRejectMessage,
        apiCall: () => this.approvalService.rejectWorkflow(id, entityId, status, reviewedBy, message),
        refresh: () => { this.getAllReviewWorkflows(); this.loadCollabrativeAgentAnalytics(); }
      },
      tool: {
        title: 'Rejecting Tool...',
        successTitle: 'Tool Rejected',
        successMessage: this.labels.toolSuccessRejectMessage,
        apiCall: () => this.approvalService.rejectTool(id, entityId, status, reviewedBy, message),
        refresh: () => { this.getAllReviewTools(); this.loadCollabrativeAgentAnalytics(); }
      }
    }[type];

    if (!contextConfig) return;

    this.dialogService.loading({
      title: contextConfig.title,
      message: 'Please wait while the rejection is processed.',
      showProgress: false,
      showCancelButton: false
    });

    contextConfig.apiCall().subscribe({
      next: (response: any) => {
        this.dialogService.close();
        const message = response?.message || contextConfig.successMessage;
        this.dialogService.success({
          title: contextConfig.successTitle,
          message
        }).then(() => {
          contextConfig.refresh();
        });
      },
      error: (error) => {
        this.dialogService.close();
        const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
        this.dialogService.error({
          title: 'Rejection Failed',
          message: errorMessage,
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.handleRejection(type, feedback, item);
          }
        });
      }
    });
  }

  handleApproval(type: 'agent' | 'workflow' | 'tool', item: any): void {
    const id = item.id;
    const reviewedBy = item.reviewedBy;
    const status = 'approved';
    const entityId = this.getEntityId(type, item);

    const contextConfig = {
      agent: {
        title: 'Approving Agent...',
        successTitle: 'Agent Approved',
        successMessage: this.labels.agentSuccessApproveMessage,
        apiCall: () => this.approvalService.approveAgent(id, entityId, status, reviewedBy),
        refresh: () => { this.getAllReviewAgents(); this.loadCollabrativeAgentAnalytics(); }
      },
      workflow: {
        title: 'Approving Workflow...',
        successTitle: 'Workflow Approved',
        successMessage: this.labels.workflowSuccessApproveMessage,
        apiCall: () => this.approvalService.approveWorkflow(id, entityId, status, reviewedBy),
        refresh: () => { this.getAllReviewWorkflows(); this.loadCollabrativeAgentAnalytics(); }
      },
      tool: {
        title: 'Approving Tool...',
        successTitle: 'Tool Approved',
        successMessage: this.labels.toolSuccessApproveMessage,
        apiCall: () => this.approvalService.approveTool(id, entityId, status, reviewedBy),
        refresh: () => { this.getAllReviewTools(); this.loadCollabrativeAgentAnalytics(); }
      }
    }[type];

    if (!contextConfig) return;

    this.dialogService.loading({
      title: contextConfig.title,
      message: 'Please wait while we approve the item.',
      showProgress: false,
      showCancelButton: false
    });

    contextConfig.apiCall().subscribe({
      next: (response: any) => {
        this.dialogService.close();
        const message = response?.message || contextConfig.successMessage;
        this.dialogService.success({
          title: contextConfig.successTitle,
          message
        }).then(() => {
          contextConfig.refresh();
        });
      },
      error: (error) => {
        this.dialogService.close();
        const errorMessage = error?.error?.message || this.labels.defaultErrorMessage;
        this.dialogService.error({
          title: 'Approval Failed',
          message: errorMessage,
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.handleApproval(type, item);
          }
        });
      }
    });
  }

  handleTesting(type: 'agent' | 'workflow' | 'tool', item: any): void {
    const entityIdKey = {
      agent: 'agentId',
      workflow: 'workflowId',
      tool: 'toolId'
    }[type];

    const entityId = item[entityIdKey];

    const routeConfig = {
      agent: {
        path: ['/build/agents/collaborative/execute'],
        queryParams: { entityId }
      },
      workflow: {
        path: ['/build/workflows/execute', entityId]
      },
      tool: {
        path: ['/libraries/tools/execute', entityId]
      }
    }[type];

    if (!routeConfig) return;

    this.router.navigate(routeConfig.path, {
      queryParams: routeConfig.queryParams || undefined
    });
  }

  getEntityId(type: 'agent' | 'workflow' | 'tool', item: any): any {
    const entityIdKey = {
      agent: 'agentId',
      workflow: 'workflowId',
      tool: 'toolId'
    }[type];

    return item[entityIdKey];
  }
}
